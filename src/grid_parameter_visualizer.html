<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网格参数可视化调节器</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f7fa;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .parameter-panel {
            width: 400px;
            background: white;
            box-shadow: 2px 0 15px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 1000;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 1.5em;
            margin-bottom: 5px;
        }
        
        .header p {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .level-config {
            padding: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .level-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .level-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .level-1 { background: #e74c3c; }
        .level-2 { background: #f39c12; }
        .level-3 { background: #27ae60; }
        
        .param-group {
            margin-bottom: 20px;
        }
        
        .param-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .param-value {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            color: #495057;
        }
        
        .param-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #e9ecef;
            outline: none;
            appearance: none;
            cursor: pointer;
        }
        
        .param-slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }
        
        .param-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }
        
        .param-info {
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .statistics {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .stat-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #495057;
        }
        
        .stat-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 2px;
        }
        
        .cost-estimate {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .cost-value {
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .cost-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .actions {
            padding: 20px;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 6px;
            font-size: 0.9em;
            margin-top: 10px;
        }
        
        .info-box {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            max-width: 300px;
            z-index: 1001;
        }
        
        .center-marker {
            width: 10px;
            height: 10px;
            background: #2c3e50;
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="parameter-panel">
            <div class="header">
                <h1>网格参数调节器</h1>
                <p>实时调整网格参数，优化扫描策略</p>
            </div>
            
            <!-- Level 1 配置 -->
            <div class="level-config">
                <div class="level-title">
                    <div class="level-icon level-1"></div>
                    Level 1 宏网格 (红色)
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>网格间距</span>
                        <span class="param-value" id="macro-spacing-value">7.0 km</span>
                    </div>
                    <input type="range" class="param-slider" id="macro-spacing" 
                           min="3" max="15" step="0.5" value="7.0">
                    <div class="param-info">控制宏网格点之间的距离，影响初始覆盖密度</div>
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>搜索半径</span>
                        <span class="param-value" id="macro-radius-value">5.0 km</span>
                    </div>
                    <input type="range" class="param-slider" id="macro-radius" 
                           min="2" max="10" step="0.5" value="5.0">
                    <div class="param-info">每个宏网格点的搜索范围，应小于网格间距</div>
                </div>
            </div>
            
            <!-- Level 2 配置 -->
            <div class="level-config">
                <div class="level-title">
                    <div class="level-icon level-2"></div>
                    Level 2 精网格 (橙色)
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>网格间距</span>
                        <span class="param-value" id="fine-spacing-value">1.4 km</span>
                    </div>
                    <input type="range" class="param-slider" id="fine-spacing" 
                           min="0.5" max="3.0" step="0.1" value="1.4">
                    <div class="param-info">精网格间距，应小于宏网格间距</div>
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>搜索半径</span>
                        <span class="param-value" id="fine-radius-value">1.0 km</span>
                    </div>
                    <input type="range" class="param-slider" id="fine-radius" 
                           min="0.3" max="2.0" step="0.1" value="1.0">
                    <div class="param-info">精网格搜索半径，建议小于网格间距</div>
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>触发阈值</span>
                        <span class="param-value" id="trigger-threshold-value">20 个</span>
                    </div>
                    <input type="range" class="param-slider" id="trigger-threshold" 
                           min="10" max="50" step="5" value="20">
                    <div class="param-info">发现多少个地点时触发Level 3增强扫描</div>
                </div>
            </div>
            
            <!-- Level 3 配置 -->
            <div class="level-config">
                <div class="level-title">
                    <div class="level-icon level-3"></div>
                    Level 3 增强网格 (绿色)
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>网格间距</span>
                        <span class="param-value" id="enhanced-spacing-value">0.7 km</span>
                    </div>
                    <input type="range" class="param-slider" id="enhanced-spacing" 
                           min="0.2" max="1.5" step="0.1" value="0.7">
                    <div class="param-info">增强扫描的网格间距，用于热点区域密集扫描</div>
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>搜索半径</span>
                        <span class="param-value" id="enhanced-radius-value">0.5 km</span>
                    </div>
                    <input type="range" class="param-slider" id="enhanced-radius" 
                           min="0.1" max="1.0" step="0.1" value="0.5">
                    <div class="param-info">增强扫描搜索半径</div>
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="statistics">
                <div class="stat-title">预估统计</div>
                
                <div class="stat-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="total-grids">0</div>
                        <div class="stat-label">总网格数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="coverage-ratio">0%</div>
                        <div class="stat-label">覆盖率</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="level1-count">0</div>
                        <div class="stat-label">Level 1</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="level2-count">0</div>
                        <div class="stat-label">Level 2</div>
                    </div>
                </div>
                
                <div class="cost-estimate">
                    <div class="cost-value" id="estimated-cost">$0</div>
                    <div class="cost-label">预估成本 (含Level 3)</div>
                </div>
                
                <div style="font-size: 0.8em; color: #666; margin-top: 10px; text-align: center;">
                    🌍 使用Haversine公式精确计算
                </div>
                
                <div id="cost-warning" class="warning" style="display: none;">
                    ⚠️ 成本可能超出预算限制
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="actions">
                <button class="btn btn-primary" onclick="exportConfig()">导出配置文件</button>
                <button class="btn btn-secondary" onclick="resetToDefaults()">重置为默认值</button>
                <button class="btn btn-secondary" onclick="loadRecommended()">加载推荐配置</button>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="info-box">
                <h4>精确地理计算</h4>
                <p><strong>🌍 Haversine公式：</strong>精确计算地球表面距离</p>
                <p><strong>📐 地球曲率：</strong>自动修正不同纬度的经度距离</p>
                <p><strong>🎯 完全覆盖：</strong>确保边界区域无遗漏</p>
                <p><strong>💡 建议：</strong>搜索半径略小于网格间距，避免过度重叠</p>
                <hr style="margin: 10px 0; border: 1px solid #eee;">
                                 <p style="font-size: 0.8em; color: #666;">
                     <strong>算法优势：</strong><br>
                     • 真实地理距离计算<br>
                     • 适用于任何纬度地区<br>
                     • 边界覆盖零遗漏<br><br>
                     <strong>💡 提示：</strong><br>
                     点击地图任意位置查看精确距离
                 </p>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // ========== 地球曲面精确计算工具函数 ==========
        
        /**
         * 使用Haversine公式计算地球表面两点间的实际距离(km)
         */
        function calculateHaversineDistance(lat1, lng1, lat2, lng2) {
            const R = 6371; // 地球半径(km)
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }
        
        /**
         * 根据起点、方位角和距离计算终点坐标
         */
        function calculateDestinationPoint(lat, lng, bearing, distance) {
            const R = 6371; // 地球半径(km)
            const δ = distance / R; // 角距离
            const φ1 = lat * Math.PI / 180; // 起点纬度(弧度)
            const λ1 = lng * Math.PI / 180; // 起点经度(弧度)
            const θ = bearing * Math.PI / 180; // 方位角(弧度)
            
            const φ2 = Math.asin(Math.sin(φ1) * Math.cos(δ) + 
                                Math.cos(φ1) * Math.sin(δ) * Math.cos(θ));
            const λ2 = λ1 + Math.atan2(Math.sin(θ) * Math.sin(δ) * Math.cos(φ1),
                                      Math.cos(δ) - Math.sin(φ1) * Math.sin(φ2));
            
            return {
                lat: φ2 * 180 / Math.PI,
                lng: ((λ2 * 180 / Math.PI) + 540) % 360 - 180 // 标准化经度
            };
        }
        
        /**
         * 生成覆盖指定圆形区域的最优网格点
         */
        function generateOptimalGrid(centerLat, centerLng, radius, spacing) {
            const grids = [];
            
            // 计算网格行数和列数
            const gridRows = Math.ceil((radius * 2) / spacing) + 2; // 多加2行确保完全覆盖
            const gridCols = Math.ceil((radius * 2) / spacing) + 2;
            
            // 计算网格起始点(西北角)
            const startPoint = calculateDestinationPoint(centerLat, centerLng, 315, radius * Math.sqrt(2));
            
            // 计算实际的纬度和经度间距
            const latStep = spacing / 111.32; // 纬度1度≈111.32km
            
            for (let row = 0; row < gridRows; row++) {
                for (let col = 0; col < gridCols; col++) {
                    // 当前行的纬度
                    const currentLat = startPoint.lat - (row * latStep);
                    
                    // 计算当前纬度下的经度间距(考虑地球曲率)
                    const lngStep = spacing / (111.32 * Math.cos(currentLat * Math.PI / 180));
                    
                    // 当前点坐标
                    const lat = currentLat;
                    const lng = startPoint.lng + (col * lngStep);
                    
                    // 使用Haversine公式检查是否在扫描范围内
                    const distance = calculateHaversineDistance(centerLat, centerLng, lat, lng);
                    
                    if (distance <= radius) {
                        grids.push({ lat, lng });
                    }
                }
            }
            
            return grids;
        }

        // 默认参数配置
        const defaultConfig = {
            centerLat: 34.0522,
            centerLng: -118.2437,
            scanRadius: 25, // km
            macroSpacing: 7.0,
            macroRadius: 5.0,
            fineSpacing: 1.4,
            fineRadius: 1.0,
            enhancedSpacing: 0.7,
            enhancedRadius: 0.5,
            triggerThreshold: 20,
            apiCostPerCall: 0.032,
            maxBudget: 200
        };

        let config = { ...defaultConfig };
        let map;
        let gridLayers = {
            level1: L.layerGroup(),
            level2: L.layerGroup(),
            level3: L.layerGroup()
        };

        // 初始化地图
        function initMap() {
            map = L.map('map').setView([config.centerLat, config.centerLng], 10);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // 添加中心点标记
            L.marker([config.centerLat, config.centerLng], {
                icon: L.divIcon({
                    className: 'center-marker',
                    iconSize: [10, 10]
                })
            }).addTo(map);

            // 添加扫描边界
            L.circle([config.centerLat, config.centerLng], {
                radius: config.scanRadius * 1000,
                fillColor: 'blue',
                fillOpacity: 0.1,
                color: 'blue',
                weight: 2
            }).addTo(map);

            // 添加图层到地图
            Object.values(gridLayers).forEach(layer => layer.addTo(map));
            
            // 生成初始网格
            generateGrids();
        }

        // 生成网格点
        function generateGrids() {
            // 清除现有网格
            Object.values(gridLayers).forEach(layer => layer.clearLayers());
            
            // 生成 Level 1 网格
            const level1Grids = generateLevel1Grid();
            
            // 生成 Level 2 网格 (部分区域)
            const level2Grids = generateLevel2Grid(level1Grids.slice(0, Math.min(3, level1Grids.length)));
            
            // 更新统计信息
            updateStatistics(level1Grids.length, level2Grids.length);
        }

        function generateLevel1Grid() {
            // 使用精确的地球曲面计算生成Level 1网格
            const gridPoints = generateOptimalGrid(
                config.centerLat, 
                config.centerLng, 
                config.scanRadius, 
                config.macroSpacing
            );
            
            const grids = [];
            
            gridPoints.forEach(point => {
                grids.push({ lat: point.lat, lng: point.lng, level: 1 });
                
                // 添加到地图
                L.circle([point.lat, point.lng], {
                    radius: config.macroRadius * 1000,
                    fillColor: '#e74c3c',
                    fillOpacity: 0.2,
                    color: '#e74c3c',
                    weight: 2
                }).addTo(gridLayers.level1);
            });
            
            return grids;
        }

        function generateLevel2Grid(triggerGrids) {
            const grids = [];
            
            triggerGrids.forEach(centerGrid => {
                // 使用精确的地球曲面计算在Level 1半径范围内生成Level 2网格
                const gridPoints = generateOptimalGrid(
                    centerGrid.lat, 
                    centerGrid.lng, 
                    config.macroRadius, 
                    config.fineSpacing
                );
                
                gridPoints.forEach(point => {
                    grids.push({ lat: point.lat, lng: point.lng, level: 2, parent: centerGrid });
                    
                    // 添加到地图
                    L.circle([point.lat, point.lng], {
                        radius: config.fineRadius * 1000,
                        fillColor: '#f39c12',
                        fillOpacity: 0.3,
                        color: '#f39c12',
                        weight: 1
                    }).addTo(gridLayers.level2);
                });
            });
            
            return grids;
        }

        // 更新统计信息
        function updateStatistics(level1Count, level2Count) {
            document.getElementById('level1-count').textContent = level1Count;
            document.getElementById('level2-count').textContent = level2Count;
            document.getElementById('total-grids').textContent = level1Count + level2Count;
            
            // 精确计算覆盖率
            const totalScanArea = Math.PI * config.scanRadius * config.scanRadius;
            
            // 估算Level 1的有效覆盖面积（考虑网格间距）
            const gridSpacing = config.macroSpacing;
            const searchRadius = config.macroRadius;
            
            // 使用更精确的覆盖率计算
            let effectiveCoverage;
            if (searchRadius >= gridSpacing) {
                // 完全覆盖
                effectiveCoverage = 100;
            } else {
                // 部分覆盖 - 基于搜索半径与网格间距的比例
                const overlapFactor = (searchRadius / gridSpacing);
                effectiveCoverage = Math.min(100, overlapFactor * 85); // 85%是经验值
            }
            
            document.getElementById('coverage-ratio').textContent = effectiveCoverage.toFixed(1) + '%';
            
            // 计算预估成本（包含可能的Level 3）
            const baseCost = (level1Count + level2Count) * config.apiCostPerCall;
            
            // 估算Level 3成本（假设20%的Level 2会触发Level 3）
            const estimatedLevel3 = Math.ceil(level2Count * 0.2 * 13); // 每个Level 2平均产生13个Level 3
            const totalEstimatedCost = (level1Count + level2Count + estimatedLevel3) * config.apiCostPerCall;
            
            document.getElementById('estimated-cost').textContent = '$' + totalEstimatedCost.toFixed(2);
            
            // 显示成本警告
            const warningElement = document.getElementById('cost-warning');
            if (totalEstimatedCost > config.maxBudget) {
                warningElement.style.display = 'block';
                warningElement.textContent = `⚠️ 预估成本($${totalEstimatedCost.toFixed(2)})可能超出预算($${config.maxBudget})`;
            } else {
                warningElement.style.display = 'none';
            }
        }

        // 参数更新处理
        function setupParameterHandlers() {
            // Macro 参数
            document.getElementById('macro-spacing').addEventListener('input', function(e) {
                config.macroSpacing = parseFloat(e.target.value);
                document.getElementById('macro-spacing-value').textContent = config.macroSpacing + ' km';
                generateGrids();
            });

            document.getElementById('macro-radius').addEventListener('input', function(e) {
                config.macroRadius = parseFloat(e.target.value);
                document.getElementById('macro-radius-value').textContent = config.macroRadius + ' km';
                generateGrids();
            });

            // Fine 参数
            document.getElementById('fine-spacing').addEventListener('input', function(e) {
                config.fineSpacing = parseFloat(e.target.value);
                document.getElementById('fine-spacing-value').textContent = config.fineSpacing + ' km';
                generateGrids();
            });

            document.getElementById('fine-radius').addEventListener('input', function(e) {
                config.fineRadius = parseFloat(e.target.value);
                document.getElementById('fine-radius-value').textContent = config.fineRadius + ' km';
                generateGrids();
            });

            // Enhanced 参数
            document.getElementById('enhanced-spacing').addEventListener('input', function(e) {
                config.enhancedSpacing = parseFloat(e.target.value);
                document.getElementById('enhanced-spacing-value').textContent = config.enhancedSpacing + ' km';
                generateGrids();
            });

            document.getElementById('enhanced-radius').addEventListener('input', function(e) {
                config.enhancedRadius = parseFloat(e.target.value);
                document.getElementById('enhanced-radius-value').textContent = config.enhancedRadius + ' km';
                generateGrids();
            });

            // 触发阈值
            document.getElementById('trigger-threshold').addEventListener('input', function(e) {
                config.triggerThreshold = parseInt(e.target.value);
                document.getElementById('trigger-threshold-value').textContent = config.triggerThreshold + ' 个';
            });
        }

        // 导出配置
        function exportConfig() {
            // 当前时间戳
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
            
            const configData = {
                // 基础网格参数
                MACRO_GRID_SPACING: config.macroSpacing,
                MACRO_SEARCH_RADIUS: Math.round(config.macroRadius * 1000), // 转换为米
                FINE_GRID_SPACING: config.fineSpacing,
                FINE_SEARCH_RADIUS: Math.round(config.fineRadius * 1000), // 转换为米
                
                // 递归参数
                RECURSION_TRIGGER_COUNT: config.triggerThreshold,
                RECURSION_SPACING_FACTOR: Number((config.enhancedSpacing / config.fineSpacing).toFixed(3)),
                RECURSION_RADIUS_FACTOR: Number((config.enhancedRadius / config.fineRadius).toFixed(3)),
                
                // 元信息
                _metadata: {
                    generated_at: new Date().toISOString(),
                    tool: "Grid Parameter Visualizer",
                    center_coordinates: [config.centerLat, config.centerLng],
                    scan_radius_km: config.scanRadius,
                    notes: "使用Haversine公式精确地理计算生成的参数配置"
                },
                
                // 计算说明
                _calculations: {
                    estimated_level1_grids: document.getElementById('level1-count').textContent,
                    estimated_level2_grids: document.getElementById('level2-count').textContent,
                    coverage_ratio: document.getElementById('coverage-ratio').textContent,
                    estimated_cost: document.getElementById('estimated-cost').textContent
                }
            };

            const dataStr = JSON.stringify(configData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `grid_config_${timestamp}.json`;
            link.click();
            
            // 显示导出成功提示
            const originalText = document.querySelector('.btn-primary').textContent;
            const btn = document.querySelector('.btn-primary');
            btn.textContent = '✅ 配置已导出';
            btn.style.background = '#27ae60';
            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = '';
            }, 2000);
        }

        // 重置为默认值
        function resetToDefaults() {
            config = { ...defaultConfig };
            updateUIValues();
            generateGrids();
        }

        // 加载推荐配置
        function loadRecommended() {
            // 基于城市规模的推荐配置
            const recommended = {
                macroSpacing: 5.0,
                macroRadius: 3.5,
                fineSpacing: 1.2,
                fineRadius: 0.8,
                enhancedSpacing: 0.6,
                enhancedRadius: 0.4,
                triggerThreshold: 15
            };
            
            Object.assign(config, recommended);
            updateUIValues();
            generateGrids();
        }

        // 更新UI值
        function updateUIValues() {
            document.getElementById('macro-spacing').value = config.macroSpacing;
            document.getElementById('macro-spacing-value').textContent = config.macroSpacing + ' km';
            document.getElementById('macro-radius').value = config.macroRadius;
            document.getElementById('macro-radius-value').textContent = config.macroRadius + ' km';
            
            document.getElementById('fine-spacing').value = config.fineSpacing;
            document.getElementById('fine-spacing-value').textContent = config.fineSpacing + ' km';
            document.getElementById('fine-radius').value = config.fineRadius;
            document.getElementById('fine-radius-value').textContent = config.fineRadius + ' km';
            
            document.getElementById('enhanced-spacing').value = config.enhancedSpacing;
            document.getElementById('enhanced-spacing-value').textContent = config.enhancedSpacing + ' km';
            document.getElementById('enhanced-radius').value = config.enhancedRadius;
            document.getElementById('enhanced-radius-value').textContent = config.enhancedRadius + ' km';
            
            document.getElementById('trigger-threshold').value = config.triggerThreshold;
            document.getElementById('trigger-threshold-value').textContent = config.triggerThreshold + ' 个';
        }

        // 添加地图点击事件，显示距离验证
        function addMapClickHandler() {
            map.on('click', function(e) {
                const clickedLat = e.latlng.lat;
                const clickedLng = e.latlng.lng;
                
                // 计算到中心点的距离
                const distanceToCenter = calculateHaversineDistance(
                    config.centerLat, config.centerLng, 
                    clickedLat, clickedLng
                );
                
                // 显示临时弹窗
                const popup = L.popup()
                    .setLatLng([clickedLat, clickedLng])
                    .setContent(`
                        <div style="text-align: center;">
                            <strong>📍 距离验证</strong><br>
                            到中心点距离: <strong>${distanceToCenter.toFixed(2)} km</strong><br>
                            <small>使用Haversine公式计算</small>
                        </div>
                    `)
                    .openOn(map);
                
                // 3秒后自动关闭
                setTimeout(() => {
                    map.closePopup(popup);
                }, 3000);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            setupParameterHandlers();
            addMapClickHandler();
        });
    </script>
</body>
</html> 