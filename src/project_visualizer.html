<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - 网格扫描可视化</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f7fa;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 400px;
            background: white;
            box-shadow: 2px 0 15px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 1.4em;
            margin-bottom: 5px;
        }
        
        .header p {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .tab-container {
            border-bottom: 2px solid #eee;
        }
        
        .tabs {
            display: flex;
        }
        
        .tab {
            flex: 1;
            padding: 15px 10px;
            text-align: center;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .tab:hover {
            background: #f8f9fa;
        }
        
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            background: #f8f9fa;
        }
        
        .tab-content {
            flex: 1;
            overflow-y: auto;
        }
        
        .tab-panel {
            display: none;
            padding: 20px;
        }
        
        .tab-panel.active {
            display: block;
        }
        
        /* 项目管理样式 */
        .project-list {
            margin-bottom: 20px;
        }
        
        .project-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        
        .project-item:hover {
            background: #e9ecef;
            transform: translateX(2px);
        }
        
        .project-item.active {
            border-left-color: #667eea;
            background: #e3f2fd;
        }
        
        .project-name {
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .project-info {
            font-size: 0.9em;
            color: #6c757d;
            line-height: 1.4;
        }
        
        .project-stats {
            margin-top: 8px;
            display: flex;
            justify-content: space-between;
            font-size: 0.8em;
        }
        
        .stat-item {
            background: white;
            padding: 4px 8px;
            border-radius: 4px;
            color: #495057;
        }
        
        .create-project {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }
        
        .form-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: flex;
            gap: 10px;
        }
        
        .form-row .form-input {
            flex: 1;
        }
        
        /* 网格控制样式 */
        .grid-controls {
            margin-top: 20px;
        }
        
        .grid-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .grid-summary {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .grid-level {
            text-align: center;
            padding: 10px;
            border-radius: 6px;
            color: white;
        }
        
        .level-1 { background: #e74c3c; }
        .level-2 { background: #f39c12; }
        .level-3 { background: #27ae60; }
        
        .grid-level-count {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .grid-level-label {
            font-size: 0.8em;
            opacity: 0.9;
        }
        
        .execution-controls {
            margin-top: 20px;
        }
        
        .execution-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .execution-stat {
            background: white;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 2px;
        }
        
        /* 参数调节样式 */
        .param-group {
            margin-bottom: 20px;
        }
        
        .param-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .param-value {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            color: #495057;
        }
        
        .param-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #e9ecef;
            outline: none;
            appearance: none;
            cursor: pointer;
        }
        
        .param-slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }
        
        .param-info {
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        /* 按钮样式 */
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        /* 工具提示样式 */
        .info-box {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            max-width: 300px;
            z-index: 1001;
        }
        
        /* 图层控制样式 */
        .layer-control {
            position: absolute;
            top: 20px;
            left: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-width: 160px;
            z-index: 1000;
        }
        
        .layer-control h5 {
            margin: 0 0 10px 0;
            font-size: 0.9em;
            color: #333;
        }
        
        .layer-toggle {
            display: block;
            margin-bottom: 8px;
            cursor: pointer;
            font-size: 0.85em;
            user-select: none;
        }
        
        .layer-toggle input {
            margin-right: 8px;
        }
        
        .layer-toggle:hover {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 4px;
        }
        
        /* 执行模式切换样式 */
        .execution-mode {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        
        .mode-toggle {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }
        
        .mode-toggle input {
            display: none;
        }
        
        .toggle-slider {
            position: relative;
            width: 50px;
            height: 24px;
            background: #dc3545;
            border-radius: 12px;
            margin-right: 10px;
            transition: background 0.3s;
        }
        
        .toggle-slider::before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .mode-toggle input:checked + .toggle-slider {
            background: #28a745;
        }
        
        .mode-toggle input:checked + .toggle-slider::before {
            transform: translateX(26px);
        }
        
        .mode-label {
            font-weight: 500;
            color: #333;
        }
        
        .mode-description {
            display: block;
            margin-top: 8px;
            color: #6c757d;
            font-size: 0.85em;
        }
        
        .mode-real {
            color: #dc3545 !important;
            font-weight: 500;
        }
        
        .mode-mock {
            color: #28a745 !important;
            font-weight: 500;
        }
        
        /* 参数预览样式 */
        .preview-control {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        
        .preview-toggle {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }
        
        .preview-toggle input {
            display: none;
        }
        
        .preview-slider {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            margin-right: 10px;
            transition: background 0.3s;
        }
        
        .preview-slider::before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .preview-toggle input:checked + .preview-slider {
            background: #667eea;
        }
        
        .preview-toggle input:checked + .preview-slider::before {
            transform: translateX(26px);
        }
        
        .preview-label {
            font-weight: 500;
            color: #333;
        }
        
        .preview-description {
            display: block;
            margin-top: 8px;
            color: #6c757d;
            font-size: 0.85em;
        }
        
        .preview-stats {
            margin-bottom: 20px;
            padding: 15px;
            background: #fff;
            border: 2px solid #667eea;
            border-radius: 8px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 0.8em;
            color: #6c757d;
        }
        
        .param-validation {
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 6px;
            font-size: 0.9em;
        }
        
        .param-validation.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .param-validation.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .param-validation.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .param-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .param-actions .btn {
            margin: 0;
        }
        
        /* 进度条样式 */
        .execution-progress {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .progress-title {
            font-weight: 600;
            color: #495057;
        }
        
        .progress-percentage {
            font-weight: bold;
            color: #007bff;
            font-size: 18px;
        }
        
        .progress-bar-container {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            border-radius: 10px;
            transition: width 0.3s ease;
            position: relative;
        }
        
        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background-image: linear-gradient(
                -45deg,
                rgba(255, 255, 255, .2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, .2) 50%,
                rgba(255, 255, 255, .2) 75%,
                transparent 75%,
                transparent
            );
            background-size: 50px 50px;
            animation: move 2s linear infinite;
        }
        
        @keyframes move {
            0% { background-position: 0 0; }
            100% { background-position: 50px 50px; }
        }
        
        .progress-details {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .progress-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        /* 模拟密集区域控制样式 */
        .mock-density-preview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .density-stats {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }
        
        .density-stat {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border-radius: 6px;
            background: white;
            border: 1px solid #ddd;
        }
        
        .stat-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .density-stat.high .stat-color {
            background: #dc3545;
        }
        
        .density-stat.medium .stat-color {
            background: #fd7e14;
        }
        
        .density-stat.low .stat-color {
            background: #28a745;
        }
        
        .stat-info {
            flex: 1;
        }
        
        .stat-label {
            display: block;
            font-size: 11px;
            color: #6c757d;
            font-weight: 500;
        }
        
        .stat-value {
            display: block;
            font-weight: bold;
            color: #495057;
            margin: 2px 0;
        }
        
        .stat-detail {
            display: block;
            font-size: 10px;
            color: #868e96;
        }
        
        .param-section {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background: #fff;
        }
        
        .info-box h4 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .info-box p {
            margin-bottom: 8px;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        /* 状态指示器 */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }
        
        .status-pending { background: #6c757d; }
        .status-running { background: #ffc107; }
        .status-completed { background: #28a745; }
        .status-failed { background: #dc3545; }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 350px;
            }
            
            .form-row {
                flex-direction: column;
            }
            
            .grid-summary {
                grid-template-columns: 1fr;
            }
        }
        
        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="header">
                <h1>网格扫描项目管理</h1>
                <p>创建 → 调参 → 生成 → 执行 → 结果</p>
            </div>
            
            <div class="tab-container">
                <div class="tabs">
                    <div class="tab active" data-tab="projects">项目</div>
                    <div class="tab" data-tab="parameters">参数</div>
                    <div class="tab" data-tab="execution">执行</div>
                </div>
            </div>
            
            <div class="tab-content">
                <!-- 项目管理面板 -->
                <div class="tab-panel active" id="projects-panel">
                    <div class="create-project">
                        <h4 style="margin-bottom: 15px;">创建新项目</h4>
                        <div class="form-group">
                            <label class="form-label">项目名称</label>
                            <input type="text" class="form-input" id="project-name" placeholder="如: co-la-data">
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">纬度</label>
                                <input type="number" class="form-input" id="project-lat" placeholder="34.0522" step="0.0001">
                            </div>
                            <div class="form-group">
                                <label class="form-label">经度</label>
                                <input type="number" class="form-input" id="project-lng" placeholder="-118.2437" step="0.0001">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">扫描半径 (km)</label>
                            <input type="number" class="form-input" id="project-radius" placeholder="25" min="1" max="100">
                        </div>
                        <div class="form-group">
                            <label class="form-label">项目描述</label>
                            <input type="text" class="form-input" id="project-desc" placeholder="项目描述（可选）">
                        </div>
                        <button class="btn btn-success" onclick="createProject()">创建项目</button>
                    </div>
                    
                    <div class="project-list">
                        <h4 style="margin-bottom: 15px;">现有项目</h4>
                        <div id="projects-container" class="loading">
                            <div class="spinner"></div>
                            <div>加载项目列表...</div>
                        </div>
                    </div>
                </div>
                
                <!-- 参数调节面板 -->
                <div class="tab-panel" id="parameters-panel">
                    <div id="parameters-content">
                        <div style="text-align: center; padding: 40px; color: #6c757d;">
                            <p>请先选择一个项目</p>
                        </div>
                    </div>
                </div>
                
                <!-- 执行控制面板 -->
                <div class="tab-panel" id="execution-panel">
                    <div id="execution-content">
                        <div style="text-align: center; padding: 40px; color: #6c757d;">
                            <p>请先选择一个项目</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="layer-control" id="layer-control">
                <h5>图层控制</h5>
                <label class="layer-toggle">
                    <input type="checkbox" id="level1-toggle" checked onchange="toggleLayer('level1')">
                    <span id="level1-label">Level 1 (宏观)</span>
                </label>
                <label class="layer-toggle">
                    <input type="checkbox" id="level2-toggle" checked onchange="toggleLayer('level2')">
                    <span id="level2-label">Level 2 (精细)</span>
                </label>
                <label class="layer-toggle">
                    <input type="checkbox" id="level3-toggle" checked onchange="toggleLayer('level3')">
                    <span id="level3-label">Level 3 (增强)</span>
                </label>
            </div>
            <div class="info-box">
                <h4>🎯 智能网格系统</h4>
                <p><strong>动态生成：</strong>Level2/3基于实际数据动态生成</p>
                <p><strong>实时预览：</strong>参数调整时可视化预览效果</p>
                <p><strong>模式切换：</strong>支持真实API和模拟模式</p>
                <p><strong>成本控制：</strong>精确的增量成本管理</p>
                <hr style="margin: 10px 0; border: 1px solid #eee;">
                <p style="font-size: 0.8em; color: #666;">
                    <strong>参数预览：</strong><br>
                    • 开启实时预览查看网格布局<br>
                    • 虚线圆圈为预览效果<br>
                    • 实时显示成本和覆盖统计<br>
                    • 智能参数合理性验证
                </p>
                <hr style="margin: 10px 0; border: 1px solid #eee;">
                <p style="font-size: 0.8em; color: #666;">
                    <strong>图层说明：</strong><br>
                    🔴 Level 1 (红) - 宏观网格<br>
                    🟠 Level 2 (橙) - 精细网格<br>
                    🟢 Level 3 (绿) - 增强网格<br>
                    ⚪ 虚线 - 参数预览
                </p>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 全局状态
        let currentProject = null;
        let map = null;
        let gridLayers = {
            level1: L.layerGroup(),
            level2: L.layerGroup(),
            level3: L.layerGroup()
        };
        let selectedGrids = new Set();
        let mockMode = true; // 默认使用模拟模式

        // 参数预览相关
        let previewMode = false;
        let previewLayers = {
            level1: L.layerGroup(),
            level2: L.layerGroup()
        };
        let previewConfig = null;

        // 进度监控相关
        let progressMonitorInterval = null;
        let isExecuting = false;
        let executionStartTime = null;

        // 模拟参数相关
        let mockConfig = null;

        // ========== 统一错误处理系统 ==========

        /**
         * 统一的错误处理和用户通知系统
         */
        class NotificationManager {
            constructor() {
                this.container = null;
                this.init();
            }

            init() {
                // 创建通知容器
                this.container = document.createElement('div');
                this.container.id = 'notification-container';
                this.container.style.cssText = `
                    position: fixed; top: 20px; right: 20px; z-index: 10000;
                    max-width: 400px; pointer-events: none;
                `;
                document.body.appendChild(this.container);
            }

            show(message, type = 'info', duration = 5000) {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    background: ${this.getBackgroundColor(type)};
                    color: ${this.getTextColor(type)};
                    padding: 15px 20px; margin-bottom: 10px;
                    border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    font-family: Arial, sans-serif; font-size: 14px;
                    pointer-events: auto; cursor: pointer;
                    transform: translateX(100%); transition: transform 0.3s ease;
                    border-left: 4px solid ${this.getBorderColor(type)};
                `;

                notification.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="flex: 1;">
                            <div style="font-weight: bold; margin-bottom: 4px;">${this.getIcon(type)} ${this.getTitle(type)}</div>
                            <div>${message}</div>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()"
                                style="background: none; border: none; color: inherit; font-size: 18px; cursor: pointer; margin-left: 10px;">×</button>
                    </div>
                `;

                this.container.appendChild(notification);

                // 动画显示
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 10);

                // 自动消失
                if (duration > 0) {
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.style.transform = 'translateX(100%)';
                            setTimeout(() => notification.remove(), 300);
                        }
                    }, duration);
                }

                return notification;
            }

            getBackgroundColor(type) {
                const colors = {
                    success: '#28a745',
                    error: '#dc3545',
                    warning: '#ffc107',
                    info: '#17a2b8'
                };
                return colors[type] || colors.info;
            }

            getTextColor(type) {
                return type === 'warning' ? '#000' : '#fff';
            }

            getBorderColor(type) {
                const colors = {
                    success: '#1e7e34',
                    error: '#bd2130',
                    warning: '#d39e00',
                    info: '#117a8b'
                };
                return colors[type] || colors.info;
            }

            getIcon(type) {
                const icons = {
                    success: '✅',
                    error: '❌',
                    warning: '⚠️',
                    info: 'ℹ️'
                };
                return icons[type] || icons.info;
            }

            getTitle(type) {
                const titles = {
                    success: '成功',
                    error: '错误',
                    warning: '警告',
                    info: '信息'
                };
                return titles[type] || titles.info;
            }

            success(message, duration = 5000) {
                return this.show(message, 'success', duration);
            }

            error(message, duration = 8000) {
                return this.show(message, 'error', duration);
            }

            warning(message, duration = 6000) {
                return this.show(message, 'warning', duration);
            }

            info(message, duration = 5000) {
                return this.show(message, 'info', duration);
            }
        }

        // 全局通知管理器
        let notificationManager = null;

        /**
         * 统一的API请求处理函数
         */
        async function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            const finalOptions = { ...defaultOptions, ...options };

            try {
                const response = await fetch(url, finalOptions);

                if (!response.ok) {
                    let errorMessage = `HTTP ${response.status}`;
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.message || errorMessage;
                    } catch (e) {
                        // 如果无法解析错误响应，使用默认错误消息
                    }
                    throw new Error(errorMessage);
                }

                return await response.json();
            } catch (error) {
                if (error.name === 'AbortError') {
                    throw new Error('请求超时，请检查网络连接');
                } else if (error.message.includes('fetch')) {
                    throw new Error('网络连接失败，请检查服务器状态');
                } else {
                    throw error;
                }
            }
        }

        /**
         * 安全的异步函数执行器
         */
        async function safeExecute(asyncFunction, errorContext = '操作') {
            try {
                return await asyncFunction();
            } catch (error) {
                console.error(`${errorContext}失败:`, error);
                notificationManager.error(`${errorContext}失败: ${error.message}`);
                throw error;
            }
        }
        
        // ========== 进度监控功能 ==========

        class ProgressMonitor {
            constructor() {
                this.interval = null;
                this.isRunning = false;
                this.startTime = null;
                this.retryCount = 0;
                this.maxRetries = 3;
            }

            start() {
                if (this.isRunning) {
                    console.log('📊 进度监控已在运行中');
                    return;
                }

                this.isRunning = true;
                this.startTime = Date.now();
                this.retryCount = 0;

                // 显示进度面板
                const progressPanel = document.getElementById('execution-progress-panel');
                if (progressPanel) {
                    progressPanel.style.display = 'block';
                }

                // 立即刷新一次
                this.refresh();

                // 每3秒自动刷新（降低频率减少服务器压力）
                this.interval = setInterval(() => this.refresh(), 3000);

                console.log('📊 进度监控已启动');
                notificationManager.info('进度监控已启动', 2000);
            }

            stop() {
                if (this.interval) {
                    clearInterval(this.interval);
                    this.interval = null;
                }

                this.isRunning = false;

                // 隐藏进度面板
                const progressPanel = document.getElementById('execution-progress-panel');
                if (progressPanel) {
                    progressPanel.style.display = 'none';
                }

                console.log('📊 进度监控已停止');
            }

            async refresh() {
                if (!currentProject || !this.isRunning) return;

                try {
                    const project = await apiRequest(`/api/projects/${currentProject.name}`, {
                        signal: AbortSignal.timeout(8000) // 8秒超时
                    });

                    this.retryCount = 0; // 重置重试计数
                    this.updateUI(project);

                    // 检查是否执行完成
                    const stats = project.stats;
                    const running = stats?.by_status?.running || 0;
                    const pending = stats?.by_status?.pending || 0;

                    if (running === 0 && pending === 0) {
                        this.stop();
                        this.showCompletionNotification(stats);
                    }

                } catch (error) {
                    this.retryCount++;
                    console.warn(`进度刷新失败 (${this.retryCount}/${this.maxRetries}):`, error.message);

                    if (this.retryCount >= this.maxRetries) {
                        console.error('进度监控连续失败，停止监控');
                        this.stop();
                        notificationManager.warning('进度监控连接失败，已自动停止');
                    }
                }
            }

            updateUI(project) {
                const stats = project.stats;
                if (!stats) return;

                // 计算进度
                const total = stats.total_grids || 1;
                const completed = stats.by_status.completed || 0;
                const failed = stats.by_status.failed || 0;
                const running = stats.by_status.running || 0;
                const pending = stats.by_status.pending || 0;

                const finished = completed + failed;
                const progressPercent = Math.round((finished / total) * 100);

                // 更新进度条
                const progressBar = document.getElementById('progress-bar');
                const progressPercentage = document.getElementById('progress-percentage');

                if (progressBar) {
                    progressBar.style.width = `${progressPercent}%`;
                }
                if (progressPercentage) {
                    progressPercentage.textContent = `${progressPercent}%`;
                }

                // 计算执行时间
                const elapsedMs = Date.now() - this.startTime;
                const elapsedSec = Math.floor(elapsedMs / 1000);
                const elapsedMin = Math.floor(elapsedSec / 60);
                const elapsedDisplay = elapsedMin > 0 ?
                    `${elapsedMin}分${elapsedSec % 60}秒` : `${elapsedSec}秒`;

                // 估算剩余时间
                let remainingDisplay = '';
                if (finished > 0 && pending > 0) {
                    const avgTimePerGrid = elapsedMs / finished;
                    const remainingMs = avgTimePerGrid * pending;
                    const remainingMin = Math.floor(remainingMs / 60000);
                    const remainingSec = Math.floor((remainingMs % 60000) / 1000);
                    remainingDisplay = remainingMin > 0 ?
                        ` | 预计剩余 ${remainingMin}分${remainingSec}秒` : ` | 预计剩余 ${remainingSec}秒`;
                }

                // 更新详细信息
                const progressDetails = document.getElementById('progress-details');
                if (progressDetails) {
                    progressDetails.innerHTML = `
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>✅ 已完成: <strong>${completed}</strong></span>
                            <span>🔄 执行中: <strong>${running}</strong></span>
                            <span>⏳ 待处理: <strong>${pending}</strong></span>
                            <span>❌ 失败: <strong>${failed}</strong></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 12px; color: #6c757d;">
                            <span>⏱️ 已执行 ${elapsedDisplay}${remainingDisplay}</span>
                            <span>💰 费用: $${stats.total_cost.toFixed(3)} | 📍 地点: ${stats.total_places}</span>
                        </div>
                    `;
                }

                // 更新主界面统计
                updateExecutionPanel(project);
            }

            showCompletionNotification(stats) {
                const completed = stats?.by_status?.completed || 0;
                const failed = stats?.by_status?.failed || 0;
                const totalCost = stats?.total_cost || 0;
                const totalPlaces = stats?.total_places || 0;

                const elapsedMs = Date.now() - this.startTime;
                const elapsedMin = Math.floor(elapsedMs / 60000);
                const elapsedSec = Math.floor((elapsedMs % 60000) / 1000);
                const elapsedDisplay = elapsedMin > 0 ?
                    `${elapsedMin}分${elapsedSec}秒` : `${elapsedSec}秒`;

                notificationManager.success(
                    `执行完成！成功: ${completed} 个，失败: ${failed} 个，费用: $${totalCost.toFixed(3)}，地点: ${totalPlaces} 个，总时间: ${elapsedDisplay}`,
                    10000
                );
            }
        }

        // 全局进度监控实例
        let progressMonitor = new ProgressMonitor();

        function startProgressMonitoring() {
            progressMonitor.start();
        }

        function stopProgressMonitoring() {
            progressMonitor.stop();
        }

        function forceStartProgressMonitoring() {
            console.log('🔧 用户强制启动进度监控');
            progressMonitor.start();
        }
        
        function showExecutionStatusNotification(runningCount, pendingCount) {
            // 显示非阻塞的执行状态通知
            console.log(`📊 检测到执行中任务: 运行中 ${runningCount}, 待处理 ${pendingCount}`);

            notificationManager.warning(
                `检测到正在执行的项目：运行中 ${runningCount} 个，待处理 ${pendingCount} 个。是否启动进度监控？`,
                0 // 不自动消失
            );
        }
        
        // refreshProgress函数已被ProgressMonitor类替代
        
        // 初始化地图
        function initMap() {
            map = L.map('map').setView([34.0522, -118.2437], 8);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // 添加图层到地图
            Object.values(gridLayers).forEach(layer => layer.addTo(map));
            Object.values(previewLayers).forEach(layer => layer.addTo(map));
            
            // 添加地图点击事件
            map.on('click', function(e) {
                if (!currentProject) return;
                
                const clickedLat = e.latlng.lat;
                const clickedLng = e.latlng.lng;
                
                // 显示点击位置信息
                const popup = L.popup()
                    .setLatLng([clickedLat, clickedLng])
                    .setContent(`
                        <div style="text-align: center;">
                            <strong>📍 位置信息</strong><br>
                            纬度: ${clickedLat.toFixed(6)}<br>
                            经度: ${clickedLng.toFixed(6)}<br>
                            <small>项目: ${currentProject.name}</small>
                        </div>
                    `)
                    .openOn(map);
                
                setTimeout(() => map.closePopup(popup), 3000);
            });
        }
        
        // 标签页切换
        function initTabs() {
            const tabs = document.querySelectorAll('.tab');
            const panels = document.querySelectorAll('.tab-panel');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // 移除所有活动状态
                    tabs.forEach(t => t.classList.remove('active'));
                    panels.forEach(p => p.classList.remove('active'));
                    
                    // 激活当前标签
                    tab.classList.add('active');
                    const targetPanel = document.getElementById(tab.dataset.tab + '-panel');
                    targetPanel.classList.add('active');
                });
            });
        }
        
        // 加载项目列表
        async function loadProjects() {
            const container = document.getElementById('projects-container');

            try {
                const projects = await apiRequest('/api/projects');

                if (projects.length === 0) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #6c757d;">
                            <p>暂无项目</p>
                            <p style="font-size: 0.9em;">创建第一个项目开始使用</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = projects.map(project => `
                    <div class="project-item" onclick="selectProject('${project.name}', event)">
                        <div class="project-name">${project.name}</div>
                        <div class="project-info">
                            📍 (${project.center[0].toFixed(4)}, ${project.center[1].toFixed(4)})
                            📏 ${project.radius}km<br>
                            📝 ${project.description}
                        </div>
                        <div class="project-stats">
                            <span class="stat-item">网格: ${project.stats.total_grids}</span>
                            <span class="stat-item">成本: $${project.stats.total_cost.toFixed(2)}</span>
                            <span class="stat-item">地点: ${project.stats.total_places}</span>
                        </div>
                    </div>
                `).join('');

            } catch (error) {
                console.error('加载项目失败:', error);
                container.innerHTML = `
                    <div style="color: #dc3545; text-align: center; padding: 20px;">
                        ❌ 加载项目失败<br>
                        <small>${error.message}</small>
                        <br><br>
                        <button class="btn btn-secondary" onclick="loadProjects()" style="margin: 0; padding: 8px 16px;">重试</button>
                    </div>
                `;
                notificationManager.error(`加载项目列表失败: ${error.message}`);
            }
        }
        
        // 创建项目
        async function createProject() {
            const name = document.getElementById('project-name').value.trim();
            const lat = parseFloat(document.getElementById('project-lat').value);
            const lng = parseFloat(document.getElementById('project-lng').value);
            const radius = parseFloat(document.getElementById('project-radius').value);
            const desc = document.getElementById('project-desc').value.trim();

            // 验证输入
            if (!name) {
                notificationManager.warning('请输入项目名称');
                return;
            }

            if (isNaN(lat) || lat < -90 || lat > 90) {
                notificationManager.warning('请输入有效的纬度 (-90 到 90)');
                return;
            }

            if (isNaN(lng) || lng < -180 || lng > 180) {
                notificationManager.warning('请输入有效的经度 (-180 到 180)');
                return;
            }

            if (isNaN(radius) || radius <= 0 || radius > 100) {
                notificationManager.warning('请输入有效的扫描半径 (1-100 km)');
                return;
            }

            try {
                const result = await apiRequest('/api/projects', {
                    method: 'POST',
                    body: JSON.stringify({
                        name,
                        center_lat: lat,
                        center_lng: lng,
                        radius,
                        description: desc || `扫描项目: ${name}`
                    })
                });

                // 清空表单
                document.getElementById('project-name').value = '';
                document.getElementById('project-lat').value = '';
                document.getElementById('project-lng').value = '';
                document.getElementById('project-radius').value = '';
                document.getElementById('project-desc').value = '';

                // 重新加载项目列表
                await loadProjects();

                // 自动选择新创建的项目
                selectProject(name);

                notificationManager.success(`项目 "${name}" 创建成功！`);

            } catch (error) {
                console.error('创建项目失败:', error);
                notificationManager.error(`创建项目失败: ${error.message}`);
            }
        }
        
        // 更新图层控制标签
        function updateLayerControlLabels(project) {
            if (!project) return;

            const level1Label = document.getElementById('level1-label');
            const level2Label = document.getElementById('level2-label');
            const level3Label = document.getElementById('level3-label');

            if (level1Label) {
                level1Label.textContent = `Level ${project.h3_res_level1} (宏观)`;
            }
            if (level2Label) {
                level2Label.textContent = `Level ${project.h3_res_level2} (精细)`;
            }
            if (level3Label) {
                level3Label.textContent = `Level ${project.h3_res_level3} (增强)`;
            }
        }

        // 选择项目
        async function selectProject(projectName, clickEvent = null) {
            try {
                // 更新项目选择状态
                document.querySelectorAll('.project-item').forEach(item => {
                    item.classList.remove('active');
                });

                // 如果是通过点击事件调用，更新点击的项目状态
                if (clickEvent && clickEvent.target) {
                    clickEvent.target.closest('.project-item').classList.add('active');
                } else {
                    // 如果是通过代码调用，找到对应的项目元素并激活
                    const projectItems = document.querySelectorAll('.project-item');
                    projectItems.forEach(item => {
                        if (item.textContent.includes(projectName)) {
                            item.classList.add('active');
                        }
                    });
                }

                // 加载项目详情
                const project = await apiRequest(`/api/projects/${projectName}`);

                currentProject = project;

                // 更新图层控制标签
                updateLayerControlLabels(project);

                // 更新地图中心
                map.setView([project.center_latitude, project.center_longitude], 10);

                // 加载项目的网格数据
                await loadProjectGrids(projectName);

                // 更新参数面板
                updateParametersPanel(project);

                // 更新执行面板
                updateExecutionPanel(project);

                // 检查执行状态并显示非阻塞提示
                const currentRunning = project.stats?.by_status?.running || 0;
                const currentPending = project.stats?.by_status?.pending || 0;
                if (currentRunning > 0 || currentPending > 100) {
                    showExecutionStatusNotification(currentRunning, currentPending);
                }

                // 检查是否有正在执行的网格，自动启动进度监控
                const runningCount = project.stats?.by_status?.running || 0;
                const pendingCount = project.stats?.by_status?.pending || 0;

                // 如果有运行中的网格或者有大量待处理网格（可能是刚启动执行），启动监控
                if ((runningCount > 0 || pendingCount > 100) && !isExecuting) {
                    console.log(`📊 检测到执行中任务 (运行中: ${runningCount}, 待处理: ${pendingCount})，启动进度监控`);
                    startProgressMonitoring();
                }

                // 额外检查：如果总网格数很大且完成率较低，很可能在执行中
                const totalGrids = project.stats?.total_grids || 0;
                const completedCount = project.stats?.by_status?.completed || 0;
                const completionRate = totalGrids > 0 ? completedCount / totalGrids : 0;

                if (totalGrids > 500 && completionRate > 0.01 && completionRate < 0.9 && !isExecuting) {
                    console.log(`📊 基于完成率检测 (${(completionRate*100).toFixed(1)}%) 启动进度监控`);
                    startProgressMonitoring();
                }

                notificationManager.success(`已选择项目: ${projectName}`);

            } catch (error) {
                console.error('加载项目失败:', error);
                notificationManager.error(`加载项目失败: ${error.message}`);
            }
        }
        
        // 加载项目网格
        async function loadProjectGrids(projectName) {
            try {
                const grids = await apiRequest(`/api/projects/${projectName}/grids`);

                // 清除现有网格
                Object.values(gridLayers).forEach(layer => layer.clearLayers());

                // 移除扫描边界（如果存在）
                if (window.scanBoundary) {
                    map.removeLayer(window.scanBoundary);
                }

                // 添加扫描边界（最底层，不阻挡点击）
                window.scanBoundary = L.circle([currentProject.center_latitude, currentProject.center_longitude], {
                    radius: currentProject.scan_radius_km * 1000,
                    fillColor: 'blue',
                    fillOpacity: 0.05,
                    color: 'blue',
                    weight: 1,
                    interactive: false  // 不拦截点击事件
                }).addTo(map);

                // H3 keys based on project config
                const l1_key = `level${currentProject.h3_res_level1}`;
                const l2_key = `level${currentProject.h3_res_level2}`;
                const l3_key = `level${currentProject.h3_res_level3}`;

                // 添加 Level 1 网格（最底层）
                (grids[l1_key] || []).forEach(grid => {
                    const circle = L.circle([grid.latitude, grid.longitude], {
                        radius: grid.search_radius,
                        fillColor: getGridColor(grid.status, 1),
                        fillOpacity: 0.2,  // 降低透明度减少遮挡
                        color: getGridColor(grid.status, 1),
                        weight: 2
                    });

                    circle.bindPopup(createGridPopup(grid));
                    circle.on('click', (e) => {
                        toggleGridSelection(grid, e);
                    });
                    circle.addTo(gridLayers.level1);
                });

                // 添加 Level 2 网格（中层）
                (grids[l2_key] || []).forEach(grid => {
                    const circle = L.circle([grid.latitude, grid.longitude], {
                        radius: grid.search_radius,
                        fillColor: getGridColor(grid.status, 2),
                        fillOpacity: 0.3,  // 略高于Level1
                        color: getGridColor(grid.status, 2),
                        weight: 2
                    });

                    circle.bindPopup(createGridPopup(grid));
                    circle.on('click', (e) => {
                        toggleGridSelection(grid, e);
                    });
                    circle.addTo(gridLayers.level2);
                });

                // 添加 Level 3 网格（如果有，最顶层）
                if (grids[l3_key]) {
                    grids[l3_key].forEach(grid => {
                        const circle = L.circle([grid.latitude, grid.longitude], {
                            radius: grid.search_radius,
                            fillColor: getGridColor(grid.status, 3),
                            fillOpacity: 0.4,  // 最高透明度，最容易点击
                            color: getGridColor(grid.status, 3),
                            weight: 2
                        });

                        circle.bindPopup(createGridPopup(grid));
                        circle.on('click', (e) => {
                            toggleGridSelection(grid, e);
                        });
                        circle.addTo(gridLayers.level3);
                    });
                }

                // 确保图层按正确顺序添加（Level3在最顶层）
                try {
                    if (gridLayers.level1 && typeof gridLayers.level1.bringToBack === 'function') {
                        gridLayers.level1.bringToBack();
                    }
                    if (gridLayers.level2 && typeof gridLayers.level2.bringToFront === 'function') {
                        gridLayers.level2.bringToFront();
                    }
                    if (gridLayers.level3 && gridLayers.level3.getLayers().length > 0 && typeof gridLayers.level3.bringToFront === 'function') {
                        gridLayers.level3.bringToFront();
                    }
                } catch (layerError) {
                    console.warn('图层顺序调整失败:', layerError);
                }

            } catch (error) {
                console.error('加载网格数据失败:', error);
                notificationManager.error(`加载网格数据失败: ${error.message}`);
            }
        }
        
        // 获取网格颜色
        function getGridColor(status, level) {
            const colorsByLevel = {
                1: {  // Level 1 - 红色系
                    pending: '#95a5a6',
                    running: '#e67e22',
                    completed: '#e74c3c',
                    failed: '#c0392b'
                },
                2: {  // Level 2 - 橙色系
                    pending: '#f39c12',
                    running: '#d68910',
                    completed: '#f39c12',
                    failed: '#d68910'
                },
                3: {  // Level 3 - 绿色系
                    pending: '#2ecc71',
                    running: '#27ae60',
                    completed: '#27ae60',
                    failed: '#229954'
                }
            };
            
            return colorsByLevel[level]?.[status] || '#95a5a6';
        }
        
        // 创建网格弹窗
        function createGridPopup(grid) {
            const statusText = {
                pending: '待执行',
                running: '执行中',
                completed: '已完成',
                failed: '失败'
            };
            
            return `
                <div style="text-align: center; min-width: 200px;">
                    <strong>Grid ${grid.h3_index}</strong><br>
                    <span class="status-indicator status-${grid.status}"></span>
                    状态: ${statusText[grid.status]}<br>
                    Res: ${grid.h3_res}<br>
                    坐标: (${grid.latitude.toFixed(6)}, ${grid.longitude.toFixed(6)})<br>
                    半径: ${grid.search_radius}m<br>
                    地点: ${grid.places_found} 个<br>
                    成本: ${grid.cost_spent.toFixed(3)}<br>
                    ${grid.last_execution ? `执行: ${grid.last_execution.slice(0, 19).replace('T', ' ')}` : ''}
                    <br><br>
                    <button onclick="executeGrid('${grid.h3_index}')" 
                            style="padding: 5px 10px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        ${grid.status === 'pending' ? '执行' : '重新执行'}
                    </button>
                </div>
            `;
        }
        
        // 网格选择切换
        function toggleGridSelection(grid, event) {
            // Leaflet事件对象需要使用originalEvent
            try {
                if (event && event.originalEvent && typeof event.originalEvent.stopPropagation === 'function') {
                    event.originalEvent.stopPropagation();
                } else if (event && typeof event.stopPropagation === 'function') {
                    event.stopPropagation();
                }
            } catch (e) {
                console.warn('无法阻止事件冒泡:', e);
            }
            
            const gridKey = grid.h3_index;
            
            if (selectedGrids.has(gridKey)) {
                selectedGrids.delete(gridKey);
            } else {
                selectedGrids.add(gridKey);
            }
            
            updateExecutionPanel(currentProject);
        }
        
        // 图层显示/隐藏切换
        function toggleLayer(level) {
            const checkbox = document.getElementById(`${level}-toggle`);
            const layer = gridLayers[level];

            if (!layer) {
                console.warn(`图层 ${level} 不存在`);
                return;
            }

            if (checkbox.checked) {
                if (!map.hasLayer(layer)) {
                    map.addLayer(layer);
                }
            } else {
                if (map.hasLayer(layer)) {
                    map.removeLayer(layer);
                }
            }
        }
        
        // 执行单个网格
        async function executeGrid(gridId) {
            if (!currentProject) return;
            
            const modeText = mockMode ? '模拟模式' : '真实API模式';
            const warningText = mockMode ? '' : '\n⚠️ 注意：这将消耗真实API费用！';
            
            if (!confirm(`确定要在${modeText}下执行 Grid ${gridId}？${warningText}`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/projects/${currentProject.name}/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        grid_ids: [gridId],
                        mock_mode: mockMode
                    })
                });
                
                if (!response.ok) {
                    throw new Error('执行失败');
                }
                
                const result = await response.json();
                
                let message = `✅ Grid ${gridId} 执行成功！\n`;
                if (result.results && result.results.length > 0) {
                    const gridResult = result.results[0];
                    message += `发现地点: ${gridResult.places_found} 个\n`;
                    message += `费用: ${gridResult.cost_spent.toFixed(3)}\n`;
                    if (gridResult.triggered_next_level) {
                        message += `🎯 触发了下一级网格生成！`;
                    }
                }
                
                alert(message);
                
                // 重新加载网格数据
                await loadProjectGrids(currentProject.name);
                await selectProject(currentProject.name);
                
            } catch (error) {
                console.error('执行网格失败:', error);
                alert(`❌ 执行失败: ${error.message}`);
            }
        }
        
        // ========== 参数预览工具函数 ==========

        /**
         * 地理计算工具类
         */
        class GeoCalculator {
            static EARTH_RADIUS_KM = 6371;

            /**
             * 使用Haversine公式计算地球表面两点间的实际距离(km)
             */
            static calculateDistance(lat1, lng1, lat2, lng2) {
                const R = this.EARTH_RADIUS_KM;
                const dLat = this.toRadians(lat2 - lat1);
                const dLng = this.toRadians(lng2 - lng1);
                const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                          Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                          Math.sin(dLng/2) * Math.sin(dLng/2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                return R * c;
            }

            /**
             * 根据起点、方位角和距离计算终点坐标
             */
            static calculateDestinationPoint(lat, lng, bearing, distance) {
                const R = this.EARTH_RADIUS_KM;
                const δ = distance / R; // 角距离
                const φ1 = this.toRadians(lat);
                const λ1 = this.toRadians(lng);
                const θ = this.toRadians(bearing);

                const φ2 = Math.asin(Math.sin(φ1) * Math.cos(δ) +
                                    Math.cos(φ1) * Math.sin(δ) * Math.cos(θ));
                const λ2 = λ1 + Math.atan2(Math.sin(θ) * Math.sin(δ) * Math.cos(φ1),
                                          Math.cos(δ) - Math.sin(φ1) * Math.sin(φ2));

                return {
                    lat: this.toDegrees(φ2),
                    lng: this.normalizeLongitude(this.toDegrees(λ2))
                };
            }

            /**
             * 计算给定纬度下的经度间距（考虑地球曲率）
             */
            static getLongitudeSpacing(latitude, distanceKm) {
                const latRad = this.toRadians(latitude);
                return distanceKm / (this.EARTH_RADIUS_KM * Math.cos(latRad)) * 180 / Math.PI;
            }

            /**
             * 计算纬度间距
             */
            static getLatitudeSpacing(distanceKm) {
                return distanceKm / this.EARTH_RADIUS_KM * 180 / Math.PI;
            }

            static toRadians(degrees) {
                return degrees * Math.PI / 180;
            }

            static toDegrees(radians) {
                return radians * 180 / Math.PI;
            }

            static normalizeLongitude(lng) {
                return ((lng + 540) % 360) - 180;
            }
        }
        
        /**
         * 改进的网格生成器类
         */
        class GridGenerator {
            /**
             * 生成覆盖指定圆形区域的最优网格点
             */
            static generateOptimalGrid(centerLat, centerLng, radiusKm, spacingKm) {
                const grids = [];

                // 计算边界框
                const latSpacing = GeoCalculator.getLatitudeSpacing(spacingKm);
                const lngSpacing = GeoCalculator.getLongitudeSpacing(centerLat, spacingKm);

                // 计算网格范围（以中心点为基准）
                const gridRange = Math.ceil(radiusKm / spacingKm) + 1; // 额外增加1确保覆盖

                for (let latStep = -gridRange; latStep <= gridRange; latStep++) {
                    for (let lngStep = -gridRange; lngStep <= gridRange; lngStep++) {
                        const lat = centerLat + (latStep * latSpacing);
                        const lng = centerLng + (lngStep * lngSpacing);

                        // 检查是否在扫描范围内
                        const distance = GeoCalculator.calculateDistance(centerLat, centerLng, lat, lng);

                        if (distance <= radiusKm) {
                            grids.push({ lat, lng, distance });
                        }
                    }
                }

                // 按距离排序，优先处理靠近中心的点
                grids.sort((a, b) => a.distance - b.distance);

                return grids;
            }

            /**
             * 计算网格覆盖统计
             */
            static calculateCoverageStats(grids, searchRadiusM) {
                if (grids.length === 0) return { coverage: 0, overlap: 0 };

                const searchRadiusKm = searchRadiusM / 1000;
                const totalArea = Math.PI * Math.pow(searchRadiusKm, 2);
                const gridArea = Math.PI * Math.pow(searchRadiusKm, 2);

                return {
                    coverage: Math.min(100, (grids.length * gridArea / totalArea) * 100),
                    overlap: Math.max(0, ((grids.length * gridArea - totalArea) / totalArea) * 100),
                    efficiency: Math.min(100, (totalArea / (grids.length * gridArea)) * 100)
                };
            }
        }
        
        /**
         * 生成参数预览网格（改进版）
         */
        function generateParameterPreview(config) {
            if (!currentProject || !config) return null;

            try {
                // 清除现有预览
                Object.values(previewLayers).forEach(layer => layer.clearLayers());

                const centerLat = currentProject.center_latitude;
                const centerLng = currentProject.center_longitude;
                const scanRadius = currentProject.scan_radius_km;

                // 使用改进的网格生成器生成Level 1预览网格
                const level1Points = GridGenerator.generateOptimalGrid(
                    centerLat,
                    centerLng,
                    scanRadius,
                    config.macro_grid_spacing
                );

                // 添加Level 1预览网格到地图
                level1Points.forEach((point, index) => {
                    const circle = L.circle([point.lat, point.lng], {
                        radius: config.macro_search_radius,
                        fillColor: '#e74c3c',
                        fillOpacity: 0.1,
                        color: '#e74c3c',
                        weight: 1,
                        dashArray: '5,5' // 虚线表示预览
                    });

                    circle.bindPopup(`
                        <div style="font-size: 12px;">
                            <strong>预览Level1网格 #${index + 1}</strong><br>
                            坐标: (${point.lat.toFixed(6)}, ${point.lng.toFixed(6)})<br>
                            搜索半径: ${config.macro_search_radius}m<br>
                            距中心: ${point.distance.toFixed(2)}km
                        </div>
                    `);

                    circle.addTo(previewLayers.level1);
                });

                // 生成部分Level 2预览网格（前几个点，避免性能问题）
                const previewCount = Math.min(5, level1Points.length);
                let level2Count = 0;
                let totalLevel2Points = [];

                for (let i = 0; i < previewCount; i++) {
                    const point = level1Points[i];
                    const level2Points = GridGenerator.generateOptimalGrid(
                        point.lat,
                        point.lng,
                        config.macro_search_radius / 1000,
                        config.fine_grid_spacing
                    );

                    level2Count += level2Points.length;
                    totalLevel2Points = totalLevel2Points.concat(level2Points);
                }

                // 添加Level 2预览网格到地图
                totalLevel2Points.forEach((l2Point, index) => {
                    const circle = L.circle([l2Point.lat, l2Point.lng], {
                        radius: config.fine_search_radius,
                        fillColor: '#f39c12',
                        fillOpacity: 0.1,
                        color: '#f39c12',
                        weight: 1,
                        dashArray: '3,3' // 虚线表示预览
                    });

                    circle.bindPopup(`
                        <div style="font-size: 12px;">
                            <strong>预览Level2网格 #${index + 1}</strong><br>
                            坐标: (${l2Point.lat.toFixed(6)}, ${l2Point.lng.toFixed(6)})<br>
                            搜索半径: ${config.fine_search_radius}m<br>
                            距中心: ${l2Point.distance.toFixed(2)}km
                        </div>
                    `);

                    circle.addTo(previewLayers.level2);
                });

                // 确保预览图层显示在地图上
                if (!map.hasLayer(previewLayers.level1)) {
                    map.addLayer(previewLayers.level1);
                }
                if (!map.hasLayer(previewLayers.level2)) {
                    map.addLayer(previewLayers.level2);
                }

                // 计算统计信息
                const estimatedLevel2Total = Math.round(level2Count * level1Points.length / previewCount);
                const totalEstimated = level1Points.length + estimatedLevel2Total;
                const baseCost = level1Points.length * (config.api_cost_per_call || 0.032);
                const estimatedTotalCost = totalEstimated * (config.api_cost_per_call || 0.032);

                // 计算覆盖效率
                const level1Coverage = GridGenerator.calculateCoverageStats(level1Points, config.macro_search_radius);
                const level2Coverage = GridGenerator.calculateCoverageStats(totalLevel2Points, config.fine_search_radius);

                return {
                    level1_count: level1Points.length,
                    level2_estimated: estimatedLevel2Total,
                    level2_preview_count: totalLevel2Points.length,
                    total_estimated: totalEstimated,
                    base_cost: baseCost,
                    estimated_total_cost: estimatedTotalCost,
                    level1_coverage: level1Coverage,
                    level2_coverage: level2Coverage,
                    preview_sample_size: previewCount
                };

            } catch (error) {
                console.error('生成参数预览失败:', error);
                notificationManager.error(`生成参数预览失败: ${error.message}`);
                return null;
            }
        }
        

        
        // 更新参数面板
        function updateParametersPanel(project) {
            const content = document.getElementById('parameters-content');
            
            // 初始化预览配置
            previewConfig = {
                macro_grid_spacing: project.macro_grid_spacing,
                macro_search_radius: project.macro_search_radius,
                fine_grid_spacing: project.fine_grid_spacing,
                fine_search_radius: project.fine_search_radius,
                recursion_trigger_count: project.recursion_trigger_count,
                api_cost_per_call: project.api_cost_per_call || 0.032
            };
            
            // 初始化模拟配置
            mockConfig = {
                mock_high_density_ratio: project.mock_high_density_ratio || 0.25,
                mock_medium_density_ratio: project.mock_medium_density_ratio || 0.35,
                mock_high_density_places: project.mock_high_density_places || 35,
                mock_medium_density_places: project.mock_medium_density_places || 15,
                mock_low_density_places: project.mock_low_density_places || 5
            };
            
            content.innerHTML = `
                <h4 style="margin-bottom: 20px;">项目参数调整: ${project.name}</h4>
                
                <!-- 预览控制 -->
                <div class="preview-control">
                    <label class="preview-toggle">
                        <input type="checkbox" id="preview-toggle" onchange="togglePreviewMode(this.checked)">
                        <span class="preview-slider"></span>
                        <span class="preview-label">实时预览</span>
                    </label>
                    <small class="preview-description">开启后可实时查看参数变化效果</small>
                </div>
                
                <!-- 预览统计 -->
                <div id="preview-stats" class="preview-stats" style="display: none;">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="level1-count">0</div>
                            <div class="stat-label">Level 1</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="level2-estimated">0</div>
                            <div class="stat-label">Level 2 (估算)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="base-cost">$0</div>
                            <div class="stat-label">基础成本</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="total-estimated-cost">$0</div>
                            <div class="stat-label">预估总成本</div>
                        </div>
                    </div>
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>Level 1 间距</span>
                        <span class="param-value" id="macro-spacing-value">${project.macro_grid_spacing} km</span>
                    </div>
                    <input type="range" class="param-slider" id="macro-spacing" 
                           min="3" max="15" step="0.5" value="${project.macro_grid_spacing}"
                           oninput="updateParameterPreview('macro_grid_spacing', this.value)">
                    <div class="param-info">宏网格间距，控制初始覆盖密度</div>
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>Level 1 半径</span>
                        <span class="param-value" id="macro-radius-value">${project.macro_search_radius/1000} km</span>
                    </div>
                    <input type="range" class="param-slider" id="macro-radius" 
                           min="2" max="10" step="0.5" value="${project.macro_search_radius/1000}"
                           oninput="updateParameterPreview('macro_search_radius', this.value * 1000)">
                    <div class="param-info">宏网格搜索半径</div>
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>Level 2 间距</span>
                        <span class="param-value" id="fine-spacing-value">${project.fine_grid_spacing} km</span>
                    </div>
                    <input type="range" class="param-slider" id="fine-spacing" 
                           min="0.5" max="3.0" step="0.1" value="${project.fine_grid_spacing}"
                           oninput="updateParameterPreview('fine_grid_spacing', this.value)">
                    <div class="param-info">精网格间距</div>
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>Level 2 半径</span>
                        <span class="param-value" id="fine-radius-value">${project.fine_search_radius/1000} km</span>
                    </div>
                    <input type="range" class="param-slider" id="fine-radius" 
                           min="0.3" max="2.0" step="0.1" value="${project.fine_search_radius/1000}"
                           oninput="updateParameterPreview('fine_search_radius', this.value * 1000)">
                    <div class="param-info">精网格搜索半径</div>
                </div>
                
                <div class="param-group">
                    <div class="param-label">
                        <span>触发阈值</span>
                        <span class="param-value" id="trigger-threshold-value">${project.recursion_trigger_count} 个</span>
                    </div>
                    <input type="range" class="param-slider" id="trigger-threshold" 
                           min="10" max="50" step="5" value="${project.recursion_trigger_count}"
                           oninput="updateParameterPreview('recursion_trigger_count', this.value)">
                    <div class="param-info">触发Level 3的地点数量</div>
                </div>
                
                <!-- 参数合理性提示 -->
                <div id="param-validation" class="param-validation"></div>
                
                <!-- 模拟模式密集区域控制 -->
                <div class="param-section" id="mock-control-section">
                    <h5 style="margin: 20px 0 15px 0; color: #495057; border-bottom: 1px solid #ddd; padding-bottom: 5px;">
                        🎭 模拟模式密集区域控制
                    </h5>
                    
                    <div class="param-group">
                        <div class="param-label">
                            <span>高密度区域比例</span>
                            <span class="param-value" id="high-density-ratio-value">${(project.mock_high_density_ratio * 100).toFixed(0)}%</span>
                        </div>
                        <input type="range" class="param-slider" id="high-density-ratio" 
                               min="0.1" max="0.5" step="0.05" value="${project.mock_high_density_ratio}"
                               oninput="updateMockParameter('mock_high_density_ratio', this.value)">
                        <div class="param-info">高密度区域占总网格的比例 (会触发Level2+3)</div>
                    </div>
                    
                    <div class="param-group">
                        <div class="param-label">
                            <span>中密度区域比例</span>
                            <span class="param-value" id="medium-density-ratio-value">${(project.mock_medium_density_ratio * 100).toFixed(0)}%</span>
                        </div>
                        <input type="range" class="param-slider" id="medium-density-ratio" 
                               min="0.1" max="0.5" step="0.05" value="${project.mock_medium_density_ratio}"
                               oninput="updateMockParameter('mock_medium_density_ratio', this.value)">
                        <div class="param-info">中密度区域占总网格的比例 (可能触发Level2)</div>
                    </div>
                    
                    <div class="param-group">
                        <div class="param-label">
                            <span>高密度地点数</span>
                            <span class="param-value" id="high-density-places-value">${project.mock_high_density_places} 个</span>
                        </div>
                        <input type="range" class="param-slider" id="high-density-places" 
                               min="25" max="50" step="5" value="${project.mock_high_density_places}"
                               oninput="updateMockParameter('mock_high_density_places', this.value)">
                        <div class="param-info">高密度区域每个网格的地点数量</div>
                    </div>
                    
                    <div class="param-group">
                        <div class="param-label">
                            <span>中密度地点数</span>
                            <span class="param-value" id="medium-density-places-value">${project.mock_medium_density_places} 个</span>
                        </div>
                        <input type="range" class="param-slider" id="medium-density-places" 
                               min="10" max="25" step="2" value="${project.mock_medium_density_places}"
                               oninput="updateMockParameter('mock_medium_density_places', this.value)">
                        <div class="param-info">中密度区域每个网格的地点数量</div>
                    </div>
                    
                    <div class="param-group">
                        <div class="param-label">
                            <span>低密度地点数</span>
                            <span class="param-value" id="low-density-places-value">${project.mock_low_density_places} 个</span>
                        </div>
                        <input type="range" class="param-slider" id="low-density-places" 
                               min="2" max="12" step="1" value="${project.mock_low_density_places}"
                               oninput="updateMockParameter('mock_low_density_places', this.value)">
                        <div class="param-info">低密度区域每个网格的地点数量</div>
                    </div>
                    
                    <div class="mock-density-preview" id="mock-density-preview">
                        <div class="density-stats">
                            <div class="density-stat high">
                                <div class="stat-color"></div>
                                <div class="stat-info">
                                    <span class="stat-label">高密度</span>
                                    <span class="stat-value">${(project.mock_high_density_ratio * 100).toFixed(0)}%</span>
                                    <span class="stat-detail">${project.mock_high_density_places}个地点</span>
                                </div>
                            </div>
                            <div class="density-stat medium">
                                <div class="stat-color"></div>
                                <div class="stat-info">
                                    <span class="stat-label">中密度</span>
                                    <span class="stat-value">${(project.mock_medium_density_ratio * 100).toFixed(0)}%</span>
                                    <span class="stat-detail">${project.mock_medium_density_places}个地点</span>
                                </div>
                            </div>
                            <div class="density-stat low">
                                <div class="stat-color"></div>
                                <div class="stat-info">
                                    <span class="stat-label">低密度</span>
                                    <span class="stat-value">${(100 - project.mock_high_density_ratio * 100 - project.mock_medium_density_ratio * 100).toFixed(0)}%</span>
                                    <span class="stat-detail">${project.mock_low_density_places}个地点</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="param-actions">
                    <button class="btn btn-primary" onclick="applyParameters()">应用参数</button>
                    <button class="btn btn-success" onclick="regenerateGrids()">重新生成网格</button>
                    <button class="btn btn-secondary" onclick="resetParameters()">重置参数</button>
                    <button class="btn btn-secondary" onclick="exportProjectConfig()">导出配置</button>
                </div>
            `;
        }
        
        // 更新执行面板
        function updateExecutionPanel(project) {
            const content = document.getElementById('execution-content');

            if (!project.stats) {
                content.innerHTML = '<div style="text-align: center; padding: 40px; color: #6c757d;"><p>加载项目统计中...</p></div>';
                return;
            }

            const stats = project.stats;

            // 动态获取level键名
            const l1_key = `level${project.h3_res_level1}`;
            const l2_key = `level${project.h3_res_level2}`;
            const l3_key = `level${project.h3_res_level3}`;

            content.innerHTML = `
                <h4 style="margin-bottom: 20px;">执行控制: ${project.name}</h4>

                <div class="grid-summary">
                    <div class="grid-level level-1">
                        <div class="grid-level-count">${stats.by_level[l1_key] || 0}</div>
                        <div class="grid-level-label">Level ${project.h3_res_level1}</div>
                    </div>
                    <div class="grid-level level-2">
                        <div class="grid-level-count">${stats.by_level[l2_key] || 0}</div>
                        <div class="grid-level-label">Level ${project.h3_res_level2}</div>
                    </div>
                    <div class="grid-level level-3">
                        <div class="grid-level-count">${stats.by_level[l3_key] || 0}</div>
                        <div class="grid-level-label">Level ${project.h3_res_level3}</div>
                    </div>
                </div>
                
                <!-- 执行进度条 -->
                <div class="execution-progress" id="execution-progress-panel" style="display: none;">
                    <div class="progress-header">
                        <span class="progress-title">📊 执行进度</span>
                        <span class="progress-percentage" id="progress-percentage">0%</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progress-bar" style="width: 0%"></div>
                    </div>
                    <div class="progress-details" id="progress-details">
                        准备开始执行...
                    </div>
                    <div class="progress-actions">
                        <button class="btn btn-secondary btn-sm" onclick="progressMonitor.stop()">停止监控</button>
                        <button class="btn btn-info btn-sm" onclick="progressMonitor.refresh()">立即刷新</button>
                        <button class="btn btn-warning btn-sm" onclick="progressMonitor.start()">🔧 重启监控</button>
                    </div>
                </div>
                
                <div class="execution-stats">
                    <div class="execution-stat">
                        <div class="stat-value">${stats.by_status.pending || 0}</div>
                        <div class="stat-label">待执行</div>
                    </div>
                    <div class="execution-stat">
                        <div class="stat-value">${stats.by_status.running || 0}</div>
                        <div class="stat-label">执行中</div>
                    </div>
                    <div class="execution-stat">
                        <div class="stat-value">${stats.by_status.completed || 0}</div>
                        <div class="stat-label">已完成</div>
                    </div>
                    <div class="execution-stat">
                        <div class="stat-value">${stats.by_status.failed || 0}</div>
                        <div class="stat-label">失败</div>
                    </div>
                </div>
                
                <div class="execution-stats">
                    <div class="execution-stat">
                        <div class="stat-value">$${stats.total_cost.toFixed(2)}</div>
                        <div class="stat-label">已花费</div>
                    </div>
                    <div class="execution-stat">
                        <div class="stat-value">${stats.total_places}</div>
                        <div class="stat-label">发现地点</div>
                    </div>
                </div>
                
                <div class="execution-mode">
                    <label class="mode-toggle">
                        <input type="checkbox" id="mock-mode-toggle" checked onchange="toggleMockMode()">
                        <span class="toggle-slider"></span>
                        <span class="mode-label">模拟模式</span>
                    </label>
                    <small class="mode-description">
                        <span id="mode-description">使用模拟数据，不消耗API费用</span>
                    </small>
                </div>
                
                <div class="execution-controls">
                    <button class="btn btn-success" onclick="executeAll()" 
                            ${stats.by_status.pending === 0 ? 'disabled' : ''}>
                        执行全部待处理 (${stats.by_status.pending || 0})
                    </button>
                    
                    <button class="btn btn-warning" onclick="executeSelected()" 
                            ${selectedGrids.size === 0 ? 'disabled' : ''}>
                        执行选中 (${selectedGrids.size})
                    </button>
                    
                    <!-- 强制启动监控按钮 -->
                    ${stats.by_status.pending > 100 || stats.by_status.running > 0 ? 
                        '<button class="btn btn-danger" onclick="forceStartProgressMonitoring()" style="font-weight: bold; margin-left: 10px;">🔧 强制启动监控</button>' : ''}
                    
                    <button class="btn btn-secondary" onclick="clearSelection()">
                        清空选择
                    </button>
                    
                    <button class="btn btn-primary" onclick="viewResults()">
                        查看结果
                    </button>
                    
                    <button class="btn btn-info" onclick="exportData()">
                        导出数据
                    </button>
                </div>
                
                ${stats.by_status.failed > 0 ? `
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 6px; margin-top: 15px;">
                        ⚠️ 有 ${stats.by_status.failed} 个网格执行失败，建议检查后重试
                    </div>
                ` : ''}
            `;
        }
        
        // API 函数（模拟）
        async function updateParameter(key, value) {
            // 这里应该调用实际的API更新项目配置
            console.log(`更新参数 ${key} = ${value}`);
            currentProject[key] = parseFloat(value);
            
            // 更新显示值
            const slider = event.target;
            const valueSpan = slider.parentElement.querySelector('.param-value');
            if (key.includes('radius')) {
                valueSpan.textContent = (value / 1000) + ' km';
            } else if (key === 'recursion_trigger_count') {
                valueSpan.textContent = value + ' 个';
            } else {
                valueSpan.textContent = value + ' km';
            }
        }
        
        async function regenerateGrids() {
            if (!currentProject) return;
            
            if (!confirm('重新生成网格将清除当前的执行状态，确定继续？')) {
                return;
            }
            
            try {
                // 安全获取按钮元素（可能通过event或者DOM查找）
                let button = null;
                let originalText = '重新生成网格';
                
                try {
                    // 如果是通过按钮点击触发，使用event.target
                    if (typeof event !== 'undefined' && event.target) {
                        button = event.target;
                        originalText = button.textContent;
                    } else {
                        // 如果是程序调用，尝试查找按钮
                        const buttons = document.querySelectorAll('button');
                        for (const btn of buttons) {
                            if (btn.textContent.includes('重新生成网格') || btn.onclick && btn.onclick.toString().includes('regenerateGrids')) {
                                button = btn;
                                originalText = btn.textContent;
                                break;
                            }
                        }
                    }
                } catch (e) {
                    console.log('无法获取按钮元素，继续执行...');
                }
                
                if (button) {
                    button.textContent = '🔄 生成中...';
                    button.disabled = true;
                }
                
                const response = await fetch(`/api/projects/${currentProject.name}/grids`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        regenerate: true
                    })
                });
                
                if (!response.ok) {
                    throw new Error('重新生成网格失败');
                }
                
                const result = await response.json();

                // 动态获取level1的键名和数量
                const l1_key = `level${currentProject.h3_res_level1}`;
                const l1_count = result.stats?.[l1_key] || 0;

                notificationManager.success(`网格重新生成成功！生成了 ${l1_count} 个Level${currentProject.h3_res_level1}网格`);

                // 重新加载项目数据
                await loadProjectGrids(currentProject.name);
                await selectProject(currentProject.name);
                
                if (button) {
                    button.textContent = originalText;
                    button.disabled = false;
                }
                
            } catch (error) {
                console.error('重新生成网格失败:', error);
                alert(`❌ 重新生成网格失败: ${error.message}`);
                
                if (button) {
                    button.textContent = originalText;
                    button.disabled = false;
                }
            }
        }
        

        
        // 模式切换
        function toggleMockMode() {
            const toggle = document.getElementById('mock-mode-toggle');
            const description = document.getElementById('mode-description');
            
            mockMode = toggle.checked;
            
            if (mockMode) {
                description.innerHTML = '<span class="mode-mock">使用模拟数据，不消耗API费用</span>';
            } else {
                description.innerHTML = '<span class="mode-real">使用真实API，将产生费用</span>';
            }
        }
        
        async function executeAll() {
            if (!currentProject) return;
            
            const pendingCount = currentProject.stats?.by_status?.pending || 0;
            if (pendingCount === 0) {
                alert('没有待执行的Grid');
                return;
            }
            
            const modeText = mockMode ? '模拟模式' : '真实API模式';
            const warningText = mockMode ? '' : '\n⚠️ 注意：这将消耗真实API费用！';
            const timeWarning = pendingCount > 50 ? `\n⏱️ 预计执行时间: ${Math.ceil(pendingCount / 10)} 分钟` : '';
            
            if (!confirm(`确定要在${modeText}下执行全部 ${pendingCount} 个待处理网格？${warningText}${timeWarning}`)) {
                return;
            }
            
            // 启动进度监控
            progressMonitor.start();

            // 显示执行进度
            const originalButton = event.target;
            const originalText = originalButton.textContent;
            originalButton.disabled = true;
            originalButton.textContent = '🔄 执行中...';

            try {
                const result = await apiRequest(`/api/projects/${currentProject.name}/execute`, {
                    method: 'POST',
                    body: JSON.stringify({
                        execute_all: true,
                        mock_mode: mockMode
                    }),
                    signal: AbortSignal.timeout(600000) // 10分钟超时
                });
                
                let message = `批量执行完成！成功: ${result.successful} 个，失败: ${result.failed} 个，发现地点: ${result.total_places} 个，费用: $${result.total_cost.toFixed(2)}`;

                if (result.triggered_next_level_count > 0) {
                    message += `，触发了 ${result.triggered_next_level_count} 个下级网格`;
                }

                if (result.results_truncated) {
                    message += `，显示前50个结果，总计 ${result.total_results} 个`;
                }

                notificationManager.success(message, 8000);

                // 重新加载项目数据
                await selectProject(currentProject.name);

            } catch (error) {
                console.error('批量执行失败:', error);

                let errorMessage = '批量执行失败: ';
                if (error.name === 'AbortError' || error.name === 'TimeoutError') {
                    errorMessage += '请求超时，可能仍在后台执行';
                } else {
                    errorMessage += error.message;
                }

                notificationManager.error(errorMessage);
            } finally {
                // 恢复按钮状态
                originalButton.disabled = false;
                originalButton.textContent = originalText;

                // 注意：不在这里停止进度监控，让它自然完成
            }
        }
        
        async function executeSelected() {
            if (selectedGrids.size === 0) return;
            
            const modeText = mockMode ? '模拟模式' : '真实API模式';
            const warningText = mockMode ? '' : '\n⚠️ 注意：这将消耗真实API费用！';
            
            if (!confirm(`确定要在${modeText}下执行选中的 ${selectedGrids.size} 个网格？${warningText}`)) {
                return;
            }
            
            // 启动进度监控
            progressMonitor.start();

            // 显示执行进度
            const originalButton = event.target;
            const originalText = originalButton.textContent;
            originalButton.disabled = true;
            originalButton.textContent = '🔄 执行中...';

            try {
                const gridIds = Array.from(selectedGrids).map(gridKey => gridKey.split('_')[0]);

                const result = await apiRequest(`/api/projects/${currentProject.name}/execute`, {
                    method: 'POST',
                    body: JSON.stringify({
                        grid_ids: gridIds,
                        mock_mode: mockMode
                    }),
                    signal: AbortSignal.timeout(300000) // 5分钟超时
                });
                
                let message = `选中Grid执行完成！成功: ${result.successful} 个，失败: ${result.failed} 个，发现地点: ${result.total_places} 个，费用: $${result.total_cost.toFixed(2)}`;

                if (result.triggered_next_level_count > 0) {
                    message += `，触发了 ${result.triggered_next_level_count} 个下级网格`;
                }

                notificationManager.success(message, 6000);

                // 清空选择并重新加载项目数据
                selectedGrids.clear();
                await selectProject(currentProject.name);

            } catch (error) {
                console.error('执行选中Grid失败:', error);

                let errorMessage = '执行失败: ';
                if (error.name === 'AbortError' || error.name === 'TimeoutError') {
                    errorMessage += '请求超时，可能仍在后台执行';
                } else {
                    errorMessage += error.message;
                }

                notificationManager.error(errorMessage);
            } finally {
                // 恢复按钮状态
                originalButton.disabled = false;
                originalButton.textContent = originalText;
            }
        }
        
        function clearSelection() {
            selectedGrids.clear();
            updateExecutionPanel(currentProject);
        }
        
        async function viewResults() {
            if (!currentProject) return;
            
            // 打开结果查看页面
            window.open(`/results/${currentProject.name}`, '_blank');
        }
        
        async function exportData() {
            if (!currentProject) return;
            
            const completedCount = currentProject.stats?.by_status?.completed || 0;
            if (completedCount === 0) {
                alert('❌ 没有已完成的网格，无法导出数据');
                return;
            }
            
            if (!confirm(`确定要导出项目 '${currentProject.name}' 的所有执行结果吗？\n\n将生成JSON、CSV、Excel和统计报告文件。`)) {
                return;
            }
            
            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = '🔄 导出中...';
            
            try {
                const response = await fetch(`/api/projects/${currentProject.name}/export`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error('数据导出失败');
                }
                
                const result = await response.json();
                
                let message = `✅ 数据导出成功！\n\n`;
                message += `📊 统计信息:\n`;
                message += `- 总地点数: ${result.total_places}\n`;
                message += `- 成功网格: ${result.successful_grids}/${result.total_grids}\n`;
                message += `- 导出文件: ${result.export_files.length} 个\n\n`;
                message += `📁 导出文件:\n`;
                
                for (const file of result.export_files) {
                    const fileName = file.split('/').pop();
                    message += `- ${fileName}\n`;
                }
                
                message += `\n💡 文件已保存到项目的 exports 目录`;
                
                alert(message);
                
            } catch (error) {
                console.error('导出数据失败:', error);
                alert(`❌ 数据导出失败: ${error.message}`);
            } finally {
                button.disabled = false;
                button.textContent = originalText;
            }
        }
        
        async function exportProjectConfig() {
            if (!currentProject) return;
            
            const config = {
                name: currentProject.name,
                parameters: {
                    macro_grid_spacing: currentProject.macro_grid_spacing,
                    macro_search_radius: currentProject.macro_search_radius,
                    fine_grid_spacing: currentProject.fine_grid_spacing,
                    fine_search_radius: currentProject.fine_search_radius,
                    recursion_trigger_count: currentProject.recursion_trigger_count
                },
                exported_at: new Date().toISOString()
            };
            
            const dataStr = JSON.stringify(config, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${currentProject.name}_config.json`;
            link.click();
        }
        
        // ========== 参数预览相关函数 ==========
        
        /**
         * 更新参数预览
         */
        function updateParameterPreview(paramName, value) {
            if (!previewConfig || !currentProject) return;
            
            // 更新预览配置
            previewConfig[paramName] = parseFloat(value);
            
            // 更新显示值
            const valueMapping = {
                'macro_grid_spacing': { element: 'macro-spacing-value', format: v => `${v} km` },
                'macro_search_radius': { element: 'macro-radius-value', format: v => `${(v/1000).toFixed(1)} km` },
                'fine_grid_spacing': { element: 'fine-spacing-value', format: v => `${v} km` },
                'fine_search_radius': { element: 'fine-radius-value', format: v => `${(v/1000).toFixed(1)} km` },
                'recursion_trigger_count': { element: 'trigger-threshold-value', format: v => `${v} 个` }
            };
            
            if (valueMapping[paramName]) {
                const mapping = valueMapping[paramName];
                const element = document.getElementById(mapping.element);
                if (element) {
                    element.textContent = mapping.format(value);
                }
            }
            
            // 验证参数
            validateParameters();
            
            // 如果启用了预览，生成预览网格
            if (previewMode) {
                const stats = generateParameterPreview(previewConfig);
                updatePreviewStats(stats);
            }
        }
        
        function updateMockParameter(paramName, value) {
            if (!mockConfig || !currentProject) return;
            
            // 更新模拟配置
            if (paramName.includes('ratio')) {
                mockConfig[paramName] = parseFloat(value);
            } else {
                mockConfig[paramName] = parseInt(value);
            }
            
            // 更新显示值
            const valueMapping = {
                'mock_high_density_ratio': { element: 'high-density-ratio-value', format: v => `${(v * 100).toFixed(0)}%` },
                'mock_medium_density_ratio': { element: 'medium-density-ratio-value', format: v => `${(v * 100).toFixed(0)}%` },
                'mock_high_density_places': { element: 'high-density-places-value', format: v => `${v} 个` },
                'mock_medium_density_places': { element: 'medium-density-places-value', format: v => `${v} 个` },
                'mock_low_density_places': { element: 'low-density-places-value', format: v => `${v} 个` }
            };
            
            if (valueMapping[paramName]) {
                const mapping = valueMapping[paramName];
                const element = document.getElementById(mapping.element);
                if (element) {
                    element.textContent = mapping.format(mockConfig[paramName]);
                }
            }
            
            // 更新密集区域预览
            updateMockDensityPreview();
        }
        
        function updateMockDensityPreview() {
            if (!mockConfig) return;
            
            const preview = document.getElementById('mock-density-preview');
            if (!preview) return;
            
            const highRatio = mockConfig.mock_high_density_ratio * 100;
            const mediumRatio = mockConfig.mock_medium_density_ratio * 100;
            const lowRatio = 100 - highRatio - mediumRatio;
            
            // 确保比例不超过100%
            if (highRatio + mediumRatio > 100) {
                const excess = (highRatio + mediumRatio) - 100;
                if (paramName === 'mock_high_density_ratio') {
                    mockConfig.mock_medium_density_ratio = Math.max(0.1, mockConfig.mock_medium_density_ratio - excess/100);
                } else {
                    mockConfig.mock_high_density_ratio = Math.max(0.1, mockConfig.mock_high_density_ratio - excess/100);
                }
            }
            
            preview.innerHTML = `
                <div class="density-stats">
                    <div class="density-stat high">
                        <div class="stat-color"></div>
                        <div class="stat-info">
                            <span class="stat-label">高密度</span>
                            <span class="stat-value">${highRatio.toFixed(0)}%</span>
                            <span class="stat-detail">${mockConfig.mock_high_density_places}个地点</span>
                        </div>
                    </div>
                    <div class="density-stat medium">
                        <div class="stat-color"></div>
                        <div class="stat-info">
                            <span class="stat-label">中密度</span>
                            <span class="stat-value">${mediumRatio.toFixed(0)}%</span>
                            <span class="stat-detail">${mockConfig.mock_medium_density_places}个地点</span>
                        </div>
                    </div>
                    <div class="density-stat low">
                        <div class="stat-color"></div>
                        <div class="stat-info">
                            <span class="stat-label">低密度</span>
                            <span class="stat-value">${lowRatio.toFixed(0)}%</span>
                            <span class="stat-detail">${mockConfig.mock_low_density_places}个地点</span>
                        </div>
                    </div>
                </div>
            `;
        }
        
        /**
         * 更新预览统计（改进版）
         */
        function updatePreviewStats(stats) {
            if (!stats) {
                console.warn('预览统计数据为空');
                return;
            }

            try {
                // 基础统计更新
                const elements = {
                    'level1-count': stats.level1_count || 0,
                    'level2-estimated': stats.level2_estimated || 0,
                    'base-cost': `$${(stats.base_cost || 0).toFixed(3)}`,
                    'total-estimated-cost': `$${(stats.estimated_total_cost || 0).toFixed(3)}`
                };

                Object.entries(elements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                });

                // 更新详细统计信息（如果存在相应的元素）
                const detailsElement = document.getElementById('preview-details');
                if (detailsElement && stats.level1_coverage && stats.level2_coverage) {
                    detailsElement.innerHTML = `
                        <div style="font-size: 12px; color: #6c757d; margin-top: 10px;">
                            <div><strong>覆盖效率分析:</strong></div>
                            <div>Level1 覆盖率: ${stats.level1_coverage.coverage.toFixed(1)}% | 效率: ${stats.level1_coverage.efficiency.toFixed(1)}%</div>
                            <div>Level2 预览覆盖率: ${stats.level2_coverage.coverage.toFixed(1)}% | 效率: ${stats.level2_coverage.efficiency.toFixed(1)}%</div>
                            <div>预览样本: ${stats.preview_sample_size}/${stats.level1_count} 个Level1网格</div>
                            <div>Level2实际预览: ${stats.level2_preview_count} 个网格</div>
                        </div>
                    `;
                }

                // 显示预览成功通知
                if (stats.level1_count > 0) {
                    notificationManager.info(
                        `预览更新完成：Level1 ${stats.level1_count} 个，Level2 预计 ${stats.level2_estimated} 个，总费用约 $${(stats.estimated_total_cost || 0).toFixed(3)}`,
                        3000
                    );
                }

            } catch (error) {
                console.error('更新预览统计失败:', error);
                notificationManager.warning('预览统计更新失败，请检查参数设置');
            }
        }
        
        /**
         * 验证参数合理性
         */
        function validateParameters() {
            if (!previewConfig) return;
            
            const validation = document.getElementById('param-validation');
            if (!validation) return;
            
            const issues = [];
            const warnings = [];
            
            // 检查搜索半径与间距的关系
            const macroRatio = previewConfig.macro_search_radius / 1000 / previewConfig.macro_grid_spacing;
            const fineRatio = previewConfig.fine_search_radius / 1000 / previewConfig.fine_grid_spacing;
            
            if (macroRatio > 1.5) {
                warnings.push(`Level 1 搜索半径过大，可能导致大量重叠 (比率: ${macroRatio.toFixed(2)})`);
            } else if (macroRatio < 0.7) {
                warnings.push(`Level 1 搜索半径偏小，可能出现覆盖缺漏 (比率: ${macroRatio.toFixed(2)})`);
            }
            
            if (fineRatio > 1.5) {
                warnings.push(`Level 2 搜索半径过大，可能导致大量重叠 (比率: ${fineRatio.toFixed(2)})`);
            } else if (fineRatio < 0.7) {
                warnings.push(`Level 2 搜索半径偏小，可能出现覆盖缺漏 (比率: ${fineRatio.toFixed(2)})`);
            }
            
            // 检查触发阈值
            if (previewConfig.recursion_trigger_count < 15) {
                warnings.push(`触发阈值较低，可能产生过多Level 2网格`);
            } else if (previewConfig.recursion_trigger_count > 40) {
                warnings.push(`触发阈值较高，可能错过密集区域`);
            }
            
            // 检查成本
            const estimatedCost = (previewConfig.level1_count || 0) * previewConfig.api_cost_per_call;
            if (estimatedCost > (currentProject.max_budget || 200)) {
                issues.push(`预估成本超出预算限制`);
            }
            
            // 显示验证结果
            if (issues.length > 0) {
                validation.className = 'param-validation error';
                validation.innerHTML = `❌ ${issues.join('<br>')}`;
                validation.style.display = 'block';
            } else if (warnings.length > 0) {
                validation.className = 'param-validation warning';
                validation.innerHTML = `⚠️ ${warnings.join('<br>')}`;
                validation.style.display = 'block';
            } else {
                validation.className = 'param-validation success';
                validation.innerHTML = `✅ 参数配置合理`;
                validation.style.display = 'block';
            }
        }
        
        /**
         * 应用参数到项目
         */
        async function applyParameters() {
            if (!currentProject || !previewConfig) return;
            
            try {
                const response = await fetch(`/api/projects/${currentProject.name}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        macro_grid_spacing: previewConfig.macro_grid_spacing,
                        macro_search_radius: previewConfig.macro_search_radius,
                        fine_grid_spacing: previewConfig.fine_grid_spacing,
                        fine_search_radius: previewConfig.fine_search_radius,
                        recursion_trigger_count: previewConfig.recursion_trigger_count,
                        // 模拟参数
                        mock_high_density_ratio: mockConfig.mock_high_density_ratio,
                        mock_medium_density_ratio: mockConfig.mock_medium_density_ratio,
                        mock_high_density_places: mockConfig.mock_high_density_places,
                        mock_medium_density_places: mockConfig.mock_medium_density_places,
                        mock_low_density_places: mockConfig.mock_low_density_places
                    })
                });
                
                if (!response.ok) {
                    throw new Error('更新参数失败');
                }
                
                const result = await response.json();
                
                // 询问是否立即重新生成网格
                const shouldRegenerate = confirm('✅ 参数已成功应用到项目！\n\n是否立即重新生成网格以应用新参数？\n(选择"确定"自动重新生成，选择"取消"稍后手动生成)');
                
                if (shouldRegenerate) {
                    // 自动触发重新生成网格
                    await regenerateGrids();
                } else {
                    // 只重新加载项目数据
                    await selectProject(currentProject.name);
                    
                    // 提示用户手动操作
                    setTimeout(() => {
                        alert('💡 提示：参数已更新，请点击"重新生成网格"按钮来应用新参数到网格布局。');
                    }, 500);
                }
                
            } catch (error) {
                console.error('应用参数失败:', error);
                alert(`❌ 应用参数失败: ${error.message}`);
            }
        }
        
        /**
         * 重置参数
         */
        function resetParameters() {
            if (!currentProject) return;
            
            if (!confirm('确定要重置所有参数到当前项目的设置？')) {
                return;
            }
            
            // 重新加载参数面板
            updateParametersPanel(currentProject);
        }
        
        /**
         * 开关预览模式时更新界面
         */
        function togglePreviewMode(enabled) {
            previewMode = enabled;
            
            const statsElement = document.getElementById('preview-stats');
            if (statsElement) {
                statsElement.style.display = enabled ? 'block' : 'none';
            }
            
            if (!enabled) {
                // 关闭预览，清除预览图层
                Object.values(previewLayers).forEach(layer => layer.clearLayers());
            } else if (previewConfig) {
                // 开启预览，生成预览网格
                const stats = generateParameterPreview(previewConfig);
                updatePreviewStats(stats);
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化通知管理器
            notificationManager = new NotificationManager();

            initMap();
            initTabs();
            loadProjects();

            // 检查URL参数，支持直接打开项目和强制启动监控
            const urlParams = new URLSearchParams(window.location.search);
            const projectParam = urlParams.get('project');
            const forceMonitor = urlParams.get('force_monitor') === 'true';

            if (projectParam) {
                // 延迟加载项目，确保其他初始化完成
                setTimeout(async () => {
                    try {
                        await selectProject(projectParam);
                        if (forceMonitor) {
                            console.log('🔧 URL参数强制启动进度监控');
                            startProgressMonitoring();
                        }
                    } catch (error) {
                        console.error('URL参数加载项目失败:', error);
                        notificationManager.error(`URL参数加载项目失败: ${error.message}`);
                    }
                }, 2000);
            }

            // 显示欢迎消息
            setTimeout(() => {
                notificationManager.info('欢迎使用网格扫描项目管理系统！', 3000);
            }, 1000);
        });
    </script>
</body>
</html> 