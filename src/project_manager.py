#!/usr/bin/env python3
"""
项目管理器 - 支持独立项目目录和grid级别的配置管理

这个模块提供：
- 项目创建和配置管理
- Grid级别的状态跟踪
- 独立项目目录结构
- 增量更新支持
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict, fields
import uuid
import threading
import time

import h3
from h3 import LatLngPoly
from geopy.distance import geodesic



@dataclass
class ProjectConfig:
    """项目配置类"""
    name: str
    description: str
    center_latitude: float
    center_longitude: float
    scan_radius_km: float
    created_at: str
    updated_at: str
    
    # H3 Grid Parameters
    h3_res_level1: int = 5
    h3_res_level2: int = 7
    h3_res_level3: int = 8 # Changed from 9 to 8
    
    # Search radius for each level in meters
    search_radius_level1: int = 5000 # Changed from 4000 to 5000
    search_radius_level2: int = 1000
    search_radius_level3: int = 500 # Changed from 300 to 500
    
    recursion_trigger_count: int = 20
    
    # API配置
    max_budget: float = 200.0
    api_cost_per_call: float = 0.032
    place_types: List[str] = None
    
    # 模拟模式密集区域控制
    mock_high_density_ratio: float = 0.25  # 高密度区域比例 (25%)
    mock_medium_density_ratio: float = 0.35  # 中密度区域比例 (35%) 
    # 剩余40%为低密度区域
    mock_high_density_places: int = 35  # 高密度区域地点数 (会触发Level2+3)
    mock_medium_density_places: int = 25  # 中密度区域地点数 (会触发Level2+3) 🔥
    mock_low_density_places: int = 5   # 低密度区域地点数 (不触发)
    
    def __post_init__(self):
        if self.place_types is None:
            self.place_types = [
                'convenience_store',
                'supermarket', 
                'grocery_store',
                'gas_station'
            ]


@dataclass
class GridStatus:
    """Grid状态跟踪 - H3 based"""
    h3_index: str
    h3_res: int
    latitude: float
    longitude: float
    search_radius: int
    
    # Execution status
    status: str = "pending"  # pending, running, completed, failed, skipped
    last_execution: Optional[str] = None
    places_found: int = 0
    triggered_next_level: bool = False
    error_message: Optional[str] = None
    
    # Cost statistics
    api_calls_made: int = 0
    cost_spent: float = 0.0
    
    # Child grids generated from this one
    child_grids: List[str] = None
    parent_grid: Optional[str] = None
    
    @property
    def grid_id(self) -> str:
        return self.h3_index

    def __post_init__(self):
        if self.child_grids is None:
            self.child_grids = []


class ProjectManager:
    """项目管理器 - 管理项目配置、grid状态和执行控制"""
    
    def __init__(self, projects_root: str = "projects"):
        self.projects_root = Path(projects_root)
        self.projects_root.mkdir(exist_ok=True)
        self._file_locks = {}  # 文件锁字典
        self._locks_lock = threading.Lock()  # 保护文件锁字典的锁
    
    def _get_file_lock(self, file_path: str) -> threading.Lock:
        """获取文件的专用锁"""
        with self._locks_lock:
            if file_path not in self._file_locks:
                self._file_locks[file_path] = threading.Lock()
            return self._file_locks[file_path]
    
    def create_project(
        self, 
        name: str,
        center_lat: float,
        center_lng: float, 
        scan_radius: float,
        description: str = ""
    ) -> str:
        """
        创建新项目
        
        Returns:
            项目目录路径
        """
        # 验证项目名称
        if not name or not name.replace('-', '').replace('_', '').isalnum():
            raise ValueError("项目名称只能包含字母、数字、连字符和下划线")
        
        project_dir = self.projects_root / name
        if project_dir.exists():
            raise ValueError(f"项目 '{name}' 已存在")
        
        # 创建项目目录结构
        project_dir.mkdir()
        (project_dir / "grids").mkdir()
        (project_dir / "sessions").mkdir() 
        (project_dir / "results").mkdir()
        (project_dir / "exports").mkdir()
        
        # 创建项目配置
        now = datetime.now().isoformat()
        config = ProjectConfig(
            name=name,
            description=description,
            center_latitude=center_lat,
            center_longitude=center_lng,
            scan_radius_km=scan_radius,
            created_at=now,
            updated_at=now
        )
        
        self._save_project_config(project_dir, config)
        
        # 创建空的grid状态文件
        self._init_grid_files(project_dir)
        
        print(f"✅ 项目 '{name}' 创建成功: {project_dir}")
        return str(project_dir)
    
    def load_project(self, name: str) -> Tuple[str, ProjectConfig]:
        """加载现有项目"""
        project_dir = self.projects_root / name
        if not project_dir.exists():
            raise ValueError(f"项目 '{name}' 不存在")
        
        config = self._load_project_config(project_dir)
        return str(project_dir), config
    
    def list_projects(self) -> List[Dict[str, Any]]:
        """列出所有项目"""
        projects = []
        for project_dir in self.projects_root.iterdir():
            if project_dir.is_dir():
                try:
                    config = self._load_project_config(project_dir)
                    
                    # 统计grid状态
                    stats = self.get_project_stats(project_dir.name)
                    
                    projects.append({
                        'name': config.name,
                        'description': config.description,
                        'center': [config.center_latitude, config.center_longitude],
                        'radius': config.scan_radius_km,
                        'created_at': config.created_at,
                        'updated_at': config.updated_at,
                        'stats': stats
                    })
                except Exception as e:
                    print(f"⚠️ 无法加载项目 {project_dir.name}: {e}")
        
        return sorted(projects, key=lambda x: x['updated_at'], reverse=True)
    
    def update_project_config(self, name: str, **kwargs) -> None:
        """更新项目配置"""
        project_dir, config = self.load_project(name)
        
        # 更新配置
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        config.updated_at = datetime.now().isoformat()
        
        self._save_project_config(Path(project_dir), config)
        print(f"✅ 项目 '{name}' 配置已更新")
    
    def clear_project_grids(self, name: str) -> None:
        """清理项目的所有网格数据"""
        project_dir, config = self.load_project(name)
        
        if not Path(project_dir).exists():
            raise ValueError(f"项目 '{name}' 不存在")
        
        grids_dir = Path(project_dir) / "grids"
        
        # 清理所有级别的网格文件
        for res in [config.h3_res_level1, config.h3_res_level2, config.h3_res_level3]:
            grid_file = grids_dir / f"level{res}_grids.json"
            if grid_file.exists():
                with open(grid_file, 'w', encoding='utf-8') as f:
                    json.dump([], f, indent=2, ensure_ascii=False)
        
        print(f"🧹 项目 '{name}' 网格数据已清理")
    
    def _get_circle_geojson(self, lat: float, lon: float, radius_km: float, num_points: int = 36) -> Dict:
        """
        Creates a GeoJSON Polygon dictionary representing a circle for H3.
        """
        center_point = (lat, lon)
        
        # Generate points for the circle
        points_lng_lat = []
        for i in range(num_points):
            angle = (i / num_points) * 360
            destination = geodesic(kilometers=radius_km).destination(center_point, angle)
            points_lng_lat.append([destination.longitude, destination.latitude])
            
        # Close the polygon by adding the first point at the end
        points_lng_lat.append(points_lng_lat[0])

        return {
            "type": "Polygon",
            "coordinates": [points_lng_lat]
        }

    def generate_grids(self, name: str) -> Dict[str, int]:
        """
        为项目生成初始H3网格配置，根据扫描半径动态选择最佳分辨率
        
        Returns:
            各级别grid数量统计
        """
        project_dir, config = self.load_project(name)
        
        # 根据扫描半径选择合适的初始H3分辨率
        scan_radius_m = config.scan_radius_km * 1000
        if scan_radius_m > config.search_radius_level1:
            initial_res = config.h3_res_level1
            initial_search_radius = config.search_radius_level1
        elif scan_radius_m > config.search_radius_level2:
            initial_res = config.h3_res_level2
            initial_search_radius = config.search_radius_level2
        else:
            initial_res = config.h3_res_level3
            initial_search_radius = config.search_radius_level3

        print(f"ℹ️ 根据扫描半径 {config.scan_radius_km}km，选择初始分辨率: {initial_res}")

        scan_area_geojson = self._get_circle_geojson(
            config.center_latitude,
            config.center_longitude,
            config.scan_radius_km * 1.05  # 增加5%的半径以确保覆盖
        )
        
        # GeoJSON coordinates are [longitude, latitude], h3.polygon_to_cells expects (latitude, longitude)
        outer_boundary = [(p[1], p[0]) for p in scan_area_geojson["coordinates"][0]]

        h3_indexes = h3.polygon_to_cells(
            LatLngPoly(outer_boundary),
            initial_res,
        )
        
        grid_statuses = []
        for h3_index in h3_indexes:
            lat, lon = h3.cell_to_latlng(h3_index)
            grid_status = GridStatus(
                h3_index=h3_index,
                h3_res=initial_res,
                latitude=lat,
                longitude=lon,
                search_radius=initial_search_radius
            )
            grid_statuses.append(asdict(grid_status))
        
        grids_dir = Path(project_dir) / "grids"
        
        # 清理所有级别的网格文件
        self.clear_project_grids(name)

        # 保存初始级别的网格
        initial_level_file = grids_dir / f"level{initial_res}_grids.json"
        with open(initial_level_file, 'w', encoding='utf-8') as f:
            json.dump(grid_statuses, f, indent=2, ensure_ascii=False)

        self.update_project_config(name, updated_at=datetime.now().isoformat())
        
        stats = {
            f'level{config.h3_res_level1}': 0,
            f'level{config.h3_res_level2}': 0,
            f'level{config.h3_res_level3}': 0
        }
        stats[f'level{initial_res}'] = len(h3_indexes)
        
        print(f"✅ 项目 '{name}' H3网格生成完成: {stats}")
        return stats
    
    def get_project_stats(self, name: str) -> Dict[str, Any]:
        """获取项目统计信息"""
        try:
            project_dir, config = self.load_project(name)
            grids_dir = Path(project_dir) / "grids"
            
            stats = {
                'total_grids': 0,
                'by_level': {
                    f'level{config.h3_res_level1}': 0,
                    f'level{config.h3_res_level2}': 0,
                    f'level{config.h3_res_level3}': 0
                },
                'by_status': {'pending': 0, 'running': 0, 'completed': 0, 'failed': 0, 'skipped': 0},
                'total_cost': 0.0,
                'total_places': 0,
                'last_execution': None
            }
            
            for res in [config.h3_res_level1, config.h3_res_level2, config.h3_res_level3]:
                level_key = f'level{res}'
                grid_file = grids_dir / f"{level_key}_grids.json"
                if grid_file.exists():
                    with open(grid_file, 'r', encoding='utf-8') as f:
                        grids = json.load(f)
                        stats['by_level'][level_key] = len(grids)
                        stats['total_grids'] += len(grids)
                        
                        for grid in grids:
                            status = grid.get('status', 'pending')
                            stats['by_status'][status] = stats['by_status'].get(status, 0) + 1
                            stats['total_cost'] += grid.get('cost_spent', 0.0)
                            stats['total_places'] += grid.get('places_found', 0)
                            
                            if grid.get('last_execution'):
                                if not stats['last_execution'] or grid['last_execution'] > stats['last_execution']:
                                    stats['last_execution'] = grid['last_execution']
            
            return stats
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_grid_status(self, name: str, h3_res: int, h3_index: str) -> Optional[GridStatus]:
        """获取特定grid的状态"""
        project_dir = self.projects_root / name
        grid_file = project_dir / "grids" / f"level{h3_res}_grids.json"
        
        if not grid_file.exists():
            return None
        
        with open(grid_file, 'r', encoding='utf-8') as f:
            grids = json.load(f)
            
        for grid_data in grids:
            if grid_data['h3_index'] == h3_index:
                return GridStatus(**grid_data)
        
        return None
    
    def update_grid_status(self, name: str, h3_res: int, h3_index: str, **updates) -> bool:
        """更新grid状态 - 线程安全版本"""
        project_dir = self.projects_root / name
        grid_file = project_dir / "grids" / f"level{h3_res}_grids.json"
        
        if not grid_file.exists():
            return False
        
        file_lock = self._get_file_lock(str(grid_file))
        
        with file_lock:
            try:
                if not grid_file.exists():
                    return False
                
                with open(grid_file, 'r', encoding='utf-8') as f:
                    grids = json.load(f)
                
                updated = False
                for grid_data in grids:
                    if grid_data['h3_index'] == h3_index:
                        grid_data.update(updates)
                        grid_data['last_execution'] = datetime.now().isoformat()
                        updated = True
                        break
                
                if updated:
                    temp_file = grid_file.with_suffix('.tmp')
                    with open(temp_file, 'w', encoding='utf-8') as f:
                        json.dump(grids, f, indent=2, ensure_ascii=False)
                    temp_file.replace(grid_file)
                
                return updated
                
            except Exception as e:
                print(f"❌ 更新grid状态失败: {e}")
                return False
    
    def get_grids_by_status(self, name: str, status: str) -> List[Dict[str, Any]]:
        """根据状态获取grid列表"""
        grids = []
        project_dir, config = self.load_project(name)
        grids_dir = Path(project_dir) / "grids"
        
        for res in [config.h3_res_level1, config.h3_res_level2, config.h3_res_level3]:
            grid_file = grids_dir / f"level{res}_grids.json"
            if grid_file.exists():
                with open(grid_file, 'r', encoding='utf-8') as f:
                    level_grids = json.load(f)
                    for grid in level_grids:
                        if grid.get('status', 'pending') == status:
                            grids.append({**grid, 'h3_res': res})
        
        return grids
    
    def _save_project_config(self, project_dir: Path, config: ProjectConfig) -> None:
        """保存项目配置"""
        config_file = project_dir / "project_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(config), f, indent=2, ensure_ascii=False)
    
    def _load_project_config(self, project_dir: Path) -> ProjectConfig:
        """加载项目配置"""
        config_file = project_dir / "project_config.json"
        if not config_file.exists():
            raise ValueError(f"项目配置文件不存在: {config_file}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Filter out unexpected fields to support loading older project configs
        expected_fields = {f.name for f in fields(ProjectConfig)}
        filtered_data = {k: v for k, v in data.items() if k in expected_fields}

        return ProjectConfig(**filtered_data)
    
    def _init_grid_files(self, project_dir: Path) -> None:
        """初始化空的grid元数据文件"""
        grids_dir = project_dir / "grids"
        
        # 网格文件将由 `generate_grids` 创建
        
        # 创建元数据文件
        metadata = {
            'created_at': datetime.now().isoformat(),
            'version': '2.0-h3',
            'schema_version': 'GridStatus_v2.0_H3',
            'description': 'H3-based Grid Status Tracking'
        }
        
        metadata_file = grids_dir / "metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

    def get_project_grids(self, name: str) -> Dict[str, List[Dict]]:
        """获取项目的所有网格数据"""
        project_dir, config = self.load_project(name)
        grids_dir = Path(project_dir) / "grids"
        
        if not Path(project_dir).exists():
            raise ValueError(f"项目 '{name}' 不存在")
        
        grid_data = {}
        
        for res in [config.h3_res_level1, config.h3_res_level2, config.h3_res_level3]:
            level_key = f'level{res}'
            grid_data[level_key] = []
            grid_file = grids_dir / f"{level_key}_grids.json"
            if grid_file.exists():
                with open(grid_file, 'r', encoding='utf-8') as f:
                    grid_data[level_key] = json.load(f)
        
        return grid_data

    def generate_child_grids(self, name: str, parent_h3_index: str, places_found: int) -> List[Dict]:
        """
        为触发阈值的H3网格动态生成下级网格
        """
        project_dir, config = self.load_project(name)
        parent_res = h3.get_resolution(parent_h3_index)
        
        parent_grid = self.get_grid_status(name, parent_res, parent_h3_index)
        if not parent_grid:
            raise ValueError(f"找不到父网格 {parent_h3_index}")
        
        if places_found < config.recursion_trigger_count:
            print(f"📊 Grid {parent_h3_index} 发现 {places_found} 个地点，未达到触发阈值 ({config.recursion_trigger_count})")
            return []
        
        if parent_res == config.h3_res_level1:
            child_res = config.h3_res_level2
            child_search_radius = config.search_radius_level2
        elif parent_res == config.h3_res_level2:
            child_res = config.h3_res_level3
            child_search_radius = config.search_radius_level3
        else:
            print(f"⚠️ 已达到最大网格级别 (res {parent_res})，不再生成下级网格")
            return []
            
        print(f"🎯 Grid {parent_h3_index} 触发下级扫描 (发现 {places_found} 个地点)")
        
        child_h3_indexes = h3.cell_to_children(parent_h3_index, child_res)
        
        child_grid_statuses = []
        for h3_index in child_h3_indexes:
            lat, lon = h3.cell_to_latlng(h3_index)
            grid_status = GridStatus(
                h3_index=h3_index,
                h3_res=child_res,
                latitude=lat,
                longitude=lon,
                search_radius=child_search_radius,
                parent_grid=parent_h3_index
            )
            child_grid_statuses.append(asdict(grid_status))
        
        grids_dir = Path(project_dir) / "grids"
        level_file = grids_dir / f"level{child_res}_grids.json"
        
        file_lock = self._get_file_lock(str(level_file))
        
        with file_lock:
            try:
                existing_grids = []
                if level_file.exists():
                    with open(level_file, 'r', encoding='utf-8') as f:
                        existing_grids = json.load(f)
                
                existing_grids.extend(child_grid_statuses)
                
                temp_file = level_file.with_suffix('.tmp')
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_grids, f, indent=2, ensure_ascii=False)
                
                temp_file.replace(level_file)
                
            except Exception as e:
                print(f"❌ 保存子网格失败: {e}")
                return []
        
        print(f"✅ 为Grid {parent_h3_index} 生成了 {len(child_grid_statuses)} 个 Level {child_res} 子网格")
        
        return child_grid_statuses


# 便利函数
def create_project(name: str, center_lat: float, center_lng: float, radius: float, description: str = "") -> str:
    """便利函数：创建新项目"""
    manager = ProjectManager()
    return manager.create_project(name, center_lat, center_lng, radius, description)


def load_project(name: str) -> Tuple[str, ProjectConfig]:
    """便利函数：加载项目"""
    manager = ProjectManager()
    return manager.load_project(name)


def list_projects() -> List[Dict[str, Any]]:
    """便利函数：列出所有项目"""
    manager = ProjectManager()
    return manager.list_projects() 